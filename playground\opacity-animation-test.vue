<template>
  <div class="opacity-animation-test">
    <h2>标签透明度动画测试</h2>
    <p>测试 default 变体下标签在浮动过程中的透明度变化效果</p>
    
    <div class="test-section">
      <h3>Default 变体透明度测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>基础透明度效果</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="用户名"
            placeholder="点击查看标签透明度变化"
            required
          />
          <p class="note">标签应该在浮动时逐渐透明，失焦时逐渐显现</p>
        </div>
        
        <div class="test-item">
          <h4>有前置图标</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="邮箱地址"
            placeholder="测试前置图标情况"
            required
            prepend-icon-inner="Mail"
          />
          <p class="note">有前置图标时透明度效果应该正常</p>
        </div>
        
        <div class="test-item">
          <h4>有前缀</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="网址"
            placeholder="测试前缀情况"
            required
            prefix="https://"
          />
          <p class="note">有前缀时透明度效果应该正常</p>
        </div>
        
        <div class="test-item">
          <h4>前置图标 + 前缀</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="用户名"
            placeholder="测试组合情况"
            required
            prepend-icon-inner="User"
            prefix="@"
          />
          <p class="note">组合情况下透明度效果应该正常</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>对比其他变体（应该没有透明度效果）</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>Filled 变体</h4>
          <SPInputField
            variant="filled"
            size="medium"
            label="用户名"
            placeholder="filled 变体不应该有透明度效果"
            required
          />
          <p class="note">filled 变体标签不应该透明</p>
        </div>
        
        <div class="test-item">
          <h4>Underlined 变体</h4>
          <SPInputField
            variant="underlined"
            size="medium"
            label="用户名"
            placeholder="underlined 变体不应该有透明度效果"
            required
          />
          <p class="note">underlined 变体标签不应该透明</p>
        </div>
        
        <div class="test-item">
          <h4>Outlined 变体</h4>
          <SPInputField
            variant="outlined"
            size="medium"
            label="用户名"
            placeholder="outlined 变体不应该有透明度效果"
            required
          />
          <p class="note">outlined 变体标签不应该透明</p>
        </div>
        
        <div class="test-item">
          <h4>Borderless 变体</h4>
          <SPInputField
            variant="borderless"
            size="medium"
            label="用户名"
            placeholder="borderless 变体不应该有透明度效果"
            required
          />
          <p class="note">borderless 变体标签不应该透明</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同状态下的透明度测试</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>错误状态</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="邮箱"
            placeholder="错误状态测试"
            required
            error
            error-message="邮箱格式不正确"
          />
          <p class="note">错误状态下透明度效果应该正常</p>
        </div>
        
        <div class="test-item">
          <h4>警告状态</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="密码"
            placeholder="警告状态测试"
            required
            warning
            warning-message="密码强度较弱"
          />
          <p class="note">警告状态下透明度效果应该正常</p>
        </div>
        
        <div class="test-item">
          <h4>成功状态</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="用户名"
            placeholder="成功状态测试"
            required
            success
            success-message="用户名可用"
          />
          <p class="note">成功状态下透明度效果应该正常</p>
        </div>
        
        <div class="test-item">
          <h4>禁用状态</h4>
          <SPInputField
            variant="default"
            size="medium"
            label="只读字段"
            placeholder="禁用状态测试"
            required
            disabled
          />
          <p class="note">禁用状态下透明度效果应该正常</p>
        </div>
      </div>
    </div>

    <div class="animation-tips">
      <h3>🎭 透明度动画测试说明</h3>
      <ul>
        <li><strong>点击输入框</strong>：观察标签在向上浮动过程中逐渐变透明</li>
        <li><strong>点击外部失焦</strong>：观察标签在向下回落过程中从透明逐渐显现</li>
        <li><strong>只有 default 变体</strong>：应该有透明度效果，其他变体不应该有</li>
        <li><strong>过渡应该平滑</strong>：透明度变化应该与位置变化同步进行</li>
        <li><strong>各种状态</strong>：错误、警告、成功、禁用状态下透明度效果都应该正常</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import SPInputField from '../packages/ui/src/components/input-field/input-field.vue'
</script>

<style scoped>
.opacity-animation-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.test-item {
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.test-item h4 {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  font-weight: 600;
}

.note {
  margin: 10px 0 0 0;
  font-size: 12px;
  color: #888;
  font-style: italic;
}

.animation-tips {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.animation-tips h3 {
  margin-top: 0;
  color: #007bff;
}

.animation-tips ul {
  margin: 0;
  padding-left: 20px;
}

.animation-tips li {
  margin-bottom: 8px;
  color: #555;
}
</style>
