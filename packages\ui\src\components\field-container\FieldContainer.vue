<!--
  FieldContainer.vue - 纯样式表单字段容器
  仅提供布局、样式和基础插槽功能
  不包含表单验证、事件处理等复杂逻辑
-->

<template>
  <div
    :class="[
      'sp-field-container',
      // 尺寸类名
      size && size !== 'medium' ? `sp-field-container--${size}` : '',
      // 变体类名
      `sp-field-container--${variant || 'default'}`,
      // 效果类名
      effect && effect !== 'none' ? `sp-field-container--effect-${effect}` : '',
      // 条件类名
      {
        'sp-field-container--disabled': computedDisabled,
        'sp-field-container--readonly': readonly,
        'sp-field-container--focused': isFocused,
        'sp-field-container--error': error || validateStateComputed === 'error',
        'sp-field-container--warning': warning || validateStateComputed === 'warning',
        'sp-field-container--success': success || validateStateComputed === 'success',
        'sp-field-container--loading': loading,
        'sp-field-container--has-outline': hasOutline,
        'sp-field-container--has-value': hasValueComputed,
        'sp-field-container--label-floating': isLabelFloating,
        'sp-field-container--persistent-label': persistentLabel,
        'sp-field-container--has-prefix': !!prefix,
        'sp-field-container--prefix-floating': !!prefix && isLabelFloating,
        'sp-field-container--has-prepend': hasPrepend,
        'sp-field-container--has-suffix': !!suffix,
        'sp-field-container--suffix-floating': !!suffix && isLabelFloating,
      },
      sizeClass
    ]"
    :style="dynamicSizeVars"
  >
    <!-- 整体容器：flex 布局包含外部图标和字段容器 -->
    <div class="sp-field-container__layout">
      <!-- 外部前置区域 -->
      <div
        v-if="$slots.prependOuter"
        class="sp-field-container__prepend-icon-outer"
      >
        <!-- 外部前置插槽 -->
        <slot
          name="prependOuter"
          :icon-size="iconSize"
        />
      </div>

      <!-- v-if="variant === 'default' && isLabelFloating" -->
      <div
        v-if="variant === 'default'"
        class="sp-field-container__outline"
      >
        <div class="sp-field-container__outline__start" />

        <div class="sp-field-container__outline__notch">
          <!-- 隐藏的占位元素，用于撑开缺口宽度 -->
          <div
            v-if="props.label"
            class="sp-field-container__outline__notch-placeholder"
            :class="{
              'sp-field-container__outline__notch-placeholder--visible':
                showPlaceholder,
            }"
          >
            <span
              v-if="props.required"
              class="sp-field-container__label-asterisk"
            >
              *
            </span>
            {{ props.label }}
          </div>
        </div>

        <div class="sp-field-container__outline__end" />
      </div>

      <!-- 字段容器 -->
      <div
        ref="wrapperRef"
        class="sp-field-container__field-wrapper sp-field-container__wrapper"
      >
        <!-- 前置区域插槽 -->
        <slot
          name="prepend"
          :prepend-classes="['sp-field-container__prepend']"
          :icon-size="iconSize"
        />

        <!-- 输入容器（包含标签和输入框） -->
        <div class="sp-field-container__input-container">
          <!-- 浮动标签 -->
          <FieldLabel
            ref="labelRef"
            :label="label"
            :field-id="fieldId"
            :required="required"
            :label-classes="[
              'sp-field-container__label',
              {
                'sp-field-container__label--floating': isLabelFloating,
                'sp-field-container__label--required': required,
              }
            ]"
          />

          <!-- 前缀区域 -->
          <div
            v-if="prefix"
            class="sp-field-container__prefix"
          >
            {{ prefix }}
          </div>

          <!-- 输入元素插槽 -->
          <slot
            :field-id="fieldId"
            :input-classes="['sp-field-container__input']"
            :input-style="{}"
            :placeholder="placeholder"
            :disabled="disabled"
            :readonly="readonly"
            :icon-size="iconSize"
          />

          <!-- 后缀区域 -->
          <div
            v-if="suffix"
            class="sp-field-container__suffix"
          >
            {{ suffix }}
          </div>
        </div>

        <!-- 后置区域插槽 -->
        <slot
          name="append"
          :append-classes="['sp-field-container__append']"
          :icon-size="iconSize"
        />

        <!-- 功能区域插槽（保持向后兼容） -->
        <slot
          name="functions"
          :functions-classes="['sp-field-container__functions']"
          :icon-size="iconSize"
        />
        <!-- </div> -->

        <!-- 加载动画条 -->
        <div
          v-if="loading"
          class="sp-field-container__loading-bar"
        >
          <div class="sp-field-container__loading-progress"></div>
        </div>

        <!-- 验证消息 -->
        <transition name="sp-field-container-message">
          <div
            v-if="validateMessage"
            :class="[
              'sp-field-container__message',
              validateStateComputed && validateStateComputed !== '' ? `sp-field-container__message--${validateStateComputed}` : ''
            ]"
          >
            {{ validateMessage }}
          </div>
        </transition>

        <!-- 帮助文本 -->
        <div
          v-if="helperText && !validateMessage"
          :class="[
            'sp-field-container__helper',
            {
              'sp-field-container__helper--error': error || validateStateComputed === 'error',
              'sp-field-container__helper--warning': validateStateComputed === 'warning',
              'sp-field-container__helper--success': validateStateComputed === 'success',
            }
          ]"
        >
          {{ helperText }}
        </div>
      </div>

      <!-- 外部后置区域 -->
      <div
        v-if="$slots.appendOuter"
        class="sp-field-container__append-icon-outer"
      >
        <!-- 外部后置插槽 -->
        <slot
          name="appendOuter"
          :icon-size="iconSize"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, useId, ref, useSlots, watch, nextTick } from 'vue'
  import { useConfigurableSize } from '../../composables/useConfigurableSize'
  import FieldLabel from './FieldLabel.vue'

  // ===== Props 接口 =====
  interface FieldContainerProps {
    /** 标签文本 */
    label?: string
    /** 占位符文本 */
    placeholder?: string
    /** 是否禁用 */
    disabled?: boolean
    /** 是否只读 */
    readonly?: boolean
    /** 是否必填 */
    required?: boolean
    /** 是否错误状态 */
    error?: boolean
    /** 是否警告状态 */
    warning?: boolean
    /** 是否成功状态 */
    success?: boolean
    /** 是否加载中 */
    loading?: boolean
    /** 外观变体 */
    variant?: 'default' | 'underlined' | 'filled' | 'unborder'
    /** 视觉效果 */
    effect?: 'none' | 'glow'
    /** 组件尺寸 */
    size?: 'small' | 'medium' | 'large'
    /** 帮助文本 */
    helperText?: string
    /** 标签是否始终浮动 */
    persistentLabel?: boolean
    /** 是否聚焦状态（外部控制） */
    focused?: boolean
    /** 是否有值（外部控制） */
    hasValue?: boolean
    /** 验证状态 */
    validateState?: 'success' | 'warning' | 'error' | ''
    /** 验证消息 */
    validateMessage?: string
    /** 前缀文本 */
    prefix?: string
    /** 后缀文本 */
    suffix?: string
  }

  // ===== Props =====
  const props = withDefaults(defineProps<FieldContainerProps>(), {
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    error: false,
    warning: false,
    success: false,
    loading: false,
    required: false,
    persistentLabel: false,
    focused: false,
    hasValue: false,
    validateState: '',
    validateMessage: '',
  })

  // ===== 基础状态 =====
  const fieldId = useId()
  const wrapperRef = ref<HTMLDivElement>()
  const labelRef = ref<InstanceType<typeof FieldLabel>>()

  // ===== 计算属性 =====
  const isFocused = computed(() => props.focused)
  const hasValueComputed = computed(() => props.hasValue)
  const computedDisabled = computed(() => props.disabled)
  const isLabelFloating = computed(
    () => props.persistentLabel || isFocused.value || hasValueComputed.value
  )

  // 图标尺寸映射
  const iconSize = computed(() => {
    const sizeMap: Record<string, number> = {
      small: 16,
      medium: 18,
      large: 20,
    }
    return sizeMap[props.size] || 18
  })



  // ===== 验证状态管理 =====
  const validateStateComputed = computed(() => {
    // 优先使用传入的 validateState
    if (props.validateState) return props.validateState

    // 根据布尔状态确定验证状态
    if (props.error) return 'error'
    if (props.warning) return 'warning'
    if (props.success) return 'success'

    return ''
  })

  // 是否显示 outline（仅默认变体且标签浮动时）
  const hasOutline = computed(
    () => props.variant === 'default' && isLabelFloating.value
  )

  // 获取插槽
  const slots = useSlots()

  // 是否有前置插槽
  const hasPrepend = computed(() => !!slots.prepend)

  // 延迟显示 placeholder，实现丝滑过渡
  const showPlaceholder = ref(false)

  watch(isLabelFloating, val => {
    if (val) {
      // 激活时：延迟显示 placeholder，让标签先移动
      setTimeout(() => {
        showPlaceholder.value = true
      }, 100)
    } else {
      // 失焦时：立即隐藏 placeholder
      showPlaceholder.value = false
    }
  })

  // 动态计算前置区域宽度
  watch(
    [hasPrepend, isLabelFloating],
    () => {
      if (!hasPrepend.value || !wrapperRef.value) return

      nextTick(() => {
        const prependElement = wrapperRef.value?.querySelector(
          '.sp-field-container__prepend'
        ) as HTMLElement

        if (prependElement) {
          const prependWidth = prependElement.offsetWidth
          // 设置 CSS 变量，用于 label 的移动距离计算
          wrapperRef.value?.style.setProperty(
            '--prepend-width',
            `${prependWidth}px`
          )
        }
      })
    },
    { flush: 'post' }
  )

  // 动态计算外部图标宽度，用于 outline 定位
  watch(
    [() => slots.prependOuter, () => slots.appendOuter, isLabelFloating],
    () => {
      if (!wrapperRef.value) return

      nextTick(() => {
        const layoutElement = wrapperRef.value?.parentElement
        if (!layoutElement) return

        // 计算外部前置图标宽度
        const prependOuterElement = layoutElement.querySelector(
          '.sp-field-container__prepend-icon-outer'
        ) as HTMLElement
        const prependOuterWidth = prependOuterElement
          ? prependOuterElement.offsetWidth + 12
          : 0 // 12px 是 gap

        // 计算外部后置图标宽度
        const appendOuterElement = layoutElement.querySelector(
          '.sp-field-container__append-icon-outer'
        ) as HTMLElement
        const appendOuterWidth = appendOuterElement
          ? appendOuterElement.offsetWidth + 12
          : 0 // 12px 是 gap

        // 设置 CSS 变量，用于 outline 定位
        layoutElement.style.setProperty(
          '--sp-field-prepend-outer-width',
          `${prependOuterWidth}px`
        )
        layoutElement.style.setProperty(
          '--sp-field-append-outer-width',
          `${appendOuterWidth}px`
        )
      })
    },
    { flush: 'post' }
  )

  // ===== 配置系统 =====
  const sizeRef = computed(() => props.size || 'medium')
  const { dynamicSizeVars, sizeClass } = useConfigurableSize(sizeRef)



  // ===== 暴露方法和状态 =====
  defineExpose({
    get wrapper() {
      return wrapperRef.value || null
    },
    // 暴露状态供子组件使用
    isFocused,
    hasValue: hasValueComputed,
    isLabelFloating,
    validateState: validateStateComputed,
  })
</script>

<script lang="ts">
  export default {
    name: 'FieldContainer',
  }
</script>
