<template>
  <div class="page-container">
    <h1>InputField 组件演示</h1>
    <p>
      带有浮动标签的输入框组件，可直接作为表单字段使用，集成了所有 Input
      组件功能
    </p>

    <div class="demo-section">
      <h2>🌟 浮动标签基础用法</h2>
      <div class="demo-item">
        <label>基础浮动标签：</label>
        <sp-input-field
          v-model:value="basicValue"
          name="basic"
          label="用户名"
          placeholder="请输入用户名"
          @change="handleChange"
        />
        <span class="value-display">当前值：{{ basicValue || '(空)' }}</span>
      </div>
      <div class="demo-item">
        <label>必填字段：</label>
        <sp-input-field
          v-model:value="requiredValue"
          name="required"
          label="邮箱地址"
          placeholder="请输入邮箱"
          required
          :rules="emailRules"
        />
        <span class="value-display">标签前有红色星号，支持验证</span>
      </div>
      <div class="demo-item">
        <label>始终浮动：</label>
        <sp-input-field
          v-model:value="persistentValue"
          name="persistent"
          label="项目名称"
          placeholder="请输入项目名称"
          persistent-label
          prefix="$"
        />
        <span class="value-display">标签始终保持在上方</span>
      </div>
      <div class="demo-item">
        <label>带帮助文本：</label>
        <sp-input-field
          v-model:value="helperValue"
          name="helper"
          label="密码"
          type="password"
          placeholder="请输入密码"
          helper-text="密码长度至少8位，包含字母和数字"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🎨 变体样式 + 浮动标签</h2>
      <div class="demo-item">
        <label>默认样式：</label>
        <sp-input-field
          v-model:value="defaultVariantValue"
          prepend-icon-inner="Mail"
          placeholder="默认边框样式"
        >
          <template #prepend>
            <sp-icon
              name="Person"
              :size="16"
              color="#409eff"
            />
            <sp-icon
              name="Person"
              :size="16"
              color="#409eff"
            />
          </template>
          <template #append>
            <sp-icon
              name="Person"
              :size="16"
              color="#409eff"
            />
          </template>
        </sp-input-field>
      </div>
      <div class="demo-item">
        <label>下划线样式：</label>
        <sp-input-field
          v-model:value="underlinedVariantValue"
          name="underlined"
          label="下划线输入框"
          variant="underlined"
          prepend-icon="Mail"
          prepend-icon-inner="Mail"
          append-icon="Settings"
          prefix="222"
          placeholder="简洁的下划线样式"
        >
          <template #prefix="slotProps">
            <div class="custom-prefix">
              <sp-icon
                name="Person"
                :size="16"
                color="#409eff"
              />
              <span class="prefix-text">用户</span>
            </div>
          </template>
        </sp-input-field>
        <span class="value-display">点击查看标签上浮和下划线扩展动画</span>
      </div>
      <div class="demo-item">
        <label>填充样式：</label>
        <sp-input-field
          v-model:value="filledVariantValue"
          name="filled"
          label="填充输入框"
          variant="filled"
          prepend-icon-inner="Mail"
          append-icon="Settings"
          prefix="222"
          placeholder="带背景的填充样式"
        />
        <span class="value-display">背景色填充，标签浮动到内部顶部</span>
      </div>

      <div class="demo-item">
        <label>方形样式：</label>
        <sp-input-field
          v-model:value="squareVariantValue"
          name="square"
          label="方形输入框"
          variant="square"
          placeholder="方正的边框样式"
        />
        <span class="value-display">无圆角，方正设计</span>
      </div>
      <div class="demo-item">
        <label>无边框样式：</label>
        <sp-input-field
          v-model:value="unborderVariantValue"
          name="unborder"
          label="无边框输入框"
          variant="unborder"
          placeholder="极简无边框设计"
        />
        <span class="value-display">完全无边框，hover 显示背景</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>✨ 发光效果 + 浮动标签</h2>
      <div class="demo-item">
        <label>默认发光：</label>
        <sp-input-field
          v-model:value="glowDefaultValue"
          name="glow-default"
          label="发光输入框"
          variant="default"
          effect="glow"
          placeholder="聚焦时有发光效果"
        />
      </div>
      <div class="demo-item">
        <label>下划线发光：</label>
        <sp-input-field
          v-model:value="glowUnderlineValue"
          name="glow-underline"
          label="发光下划线"
          variant="underlined"
          effect="glow"
          placeholder="下划线 + 发光效果"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🎨 主题色演示</h2>
      <div class="demo-item">
        <label>主题切换：</label>
        <div class="theme-selector">
          <button
            v-for="theme in availableThemes"
            :key="theme.name"
            :class="['theme-btn', { active: currentTheme === theme.name }]"
            :style="{
              backgroundColor: theme.color,
              color: getTextColor(theme.color),
            }"
            @click="switchTheme(theme.name)"
          >
            {{ theme.label }}
          </button>
        </div>
      </div>
      <div class="demo-item">
        <label>自定义颜色：</label>
        <input
          type="color"
          v-model="customThemeColor"
          @change="applyCustomTheme"
          class="color-picker"
        />
        <span class="value-display">{{ customThemeColor }}</span>
      </div>
      <div class="demo-item">
        <label>主题效果：</label>
        <div class="theme-showcase">
          <sp-input-field
            v-model:value="themeShowcaseValue"
            name="theme-showcase"
            label="主题演示"
            placeholder="查看主题色效果"
            clearable
            prepend-icon="Mail"
          />
          <sp-input-field
            v-model:value="themeShowcaseValue"
            name="theme-showcase-filled"
            label="填充样式"
            variant="filled"
            placeholder="填充样式主题演示"
            clearable
          />
          <sp-input-field
            v-model:value="themeShowcaseValue"
            name="theme-showcase-underlined"
            label="下划线样式"
            variant="underlined"
            placeholder="下划线样式主题演示"
            clearable
          />
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>📏 不同尺寸</h2>
      <div class="demo-item">
        <label>小尺寸：</label>
        <sp-input-field
          v-model:value="smallSizeValue"
          name="small"
          label="小尺寸输入框"
          size="small"
          placeholder="紧凑的小尺寸"
        />
      </div>
      <div class="demo-item">
        <label>中等尺寸：</label>
        <sp-input-field
          v-model:value="mediumSizeValue"
          name="medium"
          label="中等尺寸输入框"
          :size="45"
          placeholder="标准的中等尺寸"
        />
      </div>
      <div class="demo-item">
        <label>大尺寸：</label>
        <sp-input-field
          v-model:value="largeSizeValue"
          name="large"
          label="大尺寸输入框"
          :size="75"
          placeholder="舒适的大尺寸"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🎯 动态尺寸（数字控制）</h2>
      <div class="demo-item">
        <label>尺寸滑块：</label>
        <input
          type="range"
          v-model="customSize"
          min="30"
          max="80"
          step="5"
          class="size-slider"
        />
        <span class="value-display">当前尺寸：{{ customSize }}px</span>
      </div>
      <div class="demo-item">
        <label>动态尺寸输入框：</label>
        <sp-input-field
          v-model:value="dynamicSizeValue"
          name="dynamic"
          label="动态尺寸演示"
          :size="Number(customSize)"
          clearable
          placeholder="大小随滑块变化"
        />
        <sp-input-field
          v-model:value="dynamicSizeValue"
          name="dynamic"
          label="动态尺寸演示"
          :size="Number(customSize)"
          clearable
          prepend-icon="Mail"
          variant="filled"
          placeholder="大小随滑块变化"
        />
        <span class="value-display">通过滑块实时调整组件大小！</span>
      </div>
      <div class="demo-item">
        <sp-input-field
          v-model:value="dynamicSizeValue"
          name="dynamic"
          label="动态尺寸演示"
          :size="Number(customSize)"
          clearable
          prepend-icon="Mail"
          variant="underlined"
          placeholder="大小随滑块变化"
        />
        <sp-input-field
          v-model:value="dynamicSizeValue"
          name="dynamic"
          label="动态尺寸演示"
          :size="Number(customSize)"
          clearable
          prepend-icon="Mail"
          variant="unborder"
          placeholder="大小随滑块变化"
        />
        <sp-input-field
          v-model:value="dynamicSizeValue"
          name="dynamic"
          label="动态尺寸演示"
          :size="Number(customSize)"
          clearable
          variant="square"
          placeholder="大小随滑块变化"
        />
      </div>
      <div class="demo-item">
        <label>预设尺寸对比：</label>
        <div class="size-comparison">
          <sp-input-field
            v-model:value="size30Value"
            name="size30"
            label="30px"
            :size="30"
            clearable
            placeholder="30px高度"
          />
          <sp-input-field
            v-model:value="size48Value"
            name="size48"
            label="48px (默认)"
            :size="48"
            placeholder="48px高度"
          />
          <sp-input-field
            v-model:value="size60Value"
            name="size60"
            label="60px"
            :size="60"
            placeholder="60px高度"
          />
          <sp-input-field
            v-model:value="size80Value"
            name="size80"
            label="80px"
            :size="80"
            placeholder="80px高度"
          />
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>🖱️ 图标点击事件演示</h2>
      <div class="demo-item">
        <label>外部前缀点击：</label>
        <sp-input-field
          v-model:value="clickEventValue1"
          name="click-prepend"
          label="点击外部前缀图标"
          prepend-icon="Search"
          prepend-icon-inner="Search"
          placeholder="点击左侧外部图标"
          @click:prepend="handlePrefixClick"
        />
        <span class="value-display">
          点击了外部前缀图标：{{ prefixClickCount }}次
        </span>
      </div>
      <div class="demo-item">
        <label>内部前缀点击：</label>
        <sp-input-field
          v-model:value="clickEventValue2"
          name="click-prepend-inner"
          label="点击内部前缀图标"
          prepend-icon-inner="Search"
          placeholder="点击左侧内部图标"
          @click:prepend-inner="handlePrefixInnerClick"
        />
        <span class="value-display">
          点击了内部前缀图标：{{ prefixInnerClickCount }}次
        </span>
      </div>
      <div class="demo-item">
        <label>外部后缀点击：</label>
        <sp-input-field
          v-model:value="clickEventValue3"
          name="click-append"
          label="点击外部后缀图标"
          append-icon="Settings"
          placeholder="点击右侧外部图标"
          @click:append="handleSuffixClick"
        />
        <span class="value-display">
          点击了外部后缀图标：{{ suffixClickCount }}次
        </span>
      </div>
      <div class="demo-item">
        <label>内部后缀点击：</label>
        <sp-input-field
          v-model:value="clickEventValue4"
          name="click-append-inner"
          label="点击内部后缀图标"
          append-icon="Settings"
          placeholder="点击右侧内部图标"
          @click:append-inner="handleSuffixInnerClick"
        />
        <span class="value-display">
          点击了内部后缀图标：{{ suffixInnerClickCount }}次
        </span>
      </div>
      <div class="demo-item">
        <label>四个图标组合：</label>
        <sp-input-field
          v-model:value="clickEventValue5"
          name="click-all"
          label="所有图标点击测试"
          prepend-icon="Search"
          prepend-icon-inner="Mail"
          append-icon="Settings"
          append-icon-inner="Clear"
          placeholder="点击任意图标"
          @click:prepend="handlePrefixClick"
          @click:prepend-inner="handlePrefixInnerClick"
          @click:append="handleSuffixClick"
          @click:append-inner="handleSuffixInnerClick"
        />
        <span class="value-display">
          外部前缀: {{ prefixClickCount }}, 内部前缀:
          {{ prefixInnerClickCount }}, 外部后缀: {{ suffixClickCount }},
          内部后缀: {{ suffixInnerClickCount }}
        </span>
      </div>
      <div class="demo-item">
        <label>实际使用场景：</label>
        <sp-input-field
          v-model:value="searchValue"
          name="practical-usage"
          label="="
          prepend-icon="Search"
          prepend-icon-inner="Mail"
          append-icon="Settings"
          append-icon-inner="Clear"
          placeholder="输入搜索内容"
          @click:prepend="startSearch"
          @click:prepend-inner="toggleEmailFilter"
          @click:append="openSettings"
          @click:append-inner="clearSearch"
        />
        <span class="value-display">实际功能：搜索、邮件过滤、设置、清除</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>📝 前缀功能演示</h2>
      <div class="demo-item">
        <label>文本前缀：</label>
        <sp-input-field
          v-model:value="textPrefixValue"
          name="text-prefix"
          label="用户名"
          prefix="@"
          prepend-icon-inner="Search"
          placeholder="输入用户名"
        />
        <span class="value-display">使用 @ 符号作为前缀</span>
      </div>
      <div class="demo-item">
        <label>货币前缀：</label>
        <sp-input-field
          v-model:value="currencyPrefixValue"
          name="currency-prefix"
          label="价格"
          prefix="¥"
          type="number"
          placeholder="输入价格"
        />
        <span class="value-display">使用 ¥ 符号作为前缀</span>
      </div>
      <div class="demo-item">
        <label>URL前缀：</label>
        <sp-input-field
          v-model:value="urlPrefixValue"
          name="url-prefix"
          label="网站地址"
          prefix="https://"
          placeholder="example.com"
        />
        <span class="value-display">使用 https:// 作为前缀</span>
      </div>
      <div class="demo-item">
        <label>组合使用：</label>
        <sp-input-field
          v-model:value="combinedPrefixValue"
          name="combined-prefix"
          label="邮箱地址"
          prepend-icon="Mail"
          prefix="@"
          append-icon="Settings"
          placeholder="username"
        />
        <span class="value-display">外部图标 + 前缀 + 后置图标组合</span>
      </div>
      <div class="demo-item">
        <label>不同变体：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="variantPrefixValue"
            name="variant-prefix-filled"
            label="填充样式"
            variant="filled"
            prefix="$"
            placeholder="金额"
          />
          <sp-input-field
            v-model:value="variantPrefixValue"
            name="variant-prefix-underlined"
            label="下划线样式"
            variant="underlined"
            prefix="+"
            placeholder="电话号码"
          />
          <sp-input-field
            v-model:value="variantPrefixValue"
            name="variant-prefix-unborder"
            label="无边框样式"
            variant="unborder"
            prefix="#"
            placeholder="标签"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>状态演示：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="statusPrefixValue"
            name="status-prefix-error"
            label="错误状态"
            prefix="!"
            error
            placeholder="错误信息"
          />
          <sp-input-field
            v-model:value="statusPrefixValue"
            name="status-prefix-success"
            label="成功状态"
            prefix="✓"
            success
            placeholder="成功信息"
          />
          <sp-input-field
            v-model:value="statusPrefixValue"
            name="status-prefix-disabled"
            label="禁用状态"
            prefix="×"
            disabled
            placeholder="禁用信息"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>自定义前缀插槽：</label>
        <sp-input-field
          v-model:value="customPrefixValue"
          name="custom-prefix"
          label="自定义前缀"
          placeholder="使用自定义前缀内容"
        >
          <template #prefix="slotProps">
            <div class="custom-prefix">
              <sp-icon
                name="Person"
                :size="16"
                color="#409eff"
              />
              <span class="prefix-text">用户</span>
            </div>
          </template>
        </sp-input-field>
        <span class="value-display">使用插槽自定义前缀内容（图标+文字）</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>📝 后缀功能演示</h2>
      <div class="demo-item">
        <label>文本后缀：</label>
        <sp-input-field
          v-model:value="textSuffixValue"
          name="text-suffix"
          label="域名"
          suffix=".com"
          placeholder="输入域名"
        />
        <span class="value-display">使用 .com 作为后缀</span>
      </div>
      <div class="demo-item">
        <label>单位后缀：</label>
        <sp-input-field
          v-model:value="unitSuffixValue"
          name="unit-suffix"
          label="重量"
          suffix="kg"
          type="number"
          placeholder="输入重量"
        />
        <span class="value-display">使用 kg 作为单位后缀</span>
      </div>
      <div class="demo-item">
        <label>百分比后缀：</label>
        <sp-input-field
          v-model:value="percentSuffixValue"
          name="percent-suffix"
          label="完成度"
          suffix="%"
          type="number"
          :min="0"
          :max="100"
          placeholder="输入百分比"
        />
        <span class="value-display">使用 % 作为百分比后缀</span>
      </div>
      <div class="demo-item">
        <label>前缀 + 后缀组合：</label>
        <sp-input-field
          v-model:value="prefixSuffixComboValue2"
          name="prefix-suffix-combo"
          label="价格范围"
          prefix="¥"
          suffix=".00"
          type="number"
          placeholder="输入价格"
        />
        <span class="value-display">前缀 ¥ + 后缀 .00 组合使用</span>
      </div>
      <div class="demo-item">
        <label>不同变体：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="variantSuffixValue2"
            name="variant-suffix-filled"
            label="填充样式"
            variant="filled"
            suffix="MB"
            placeholder="文件大小"
          />
          <sp-input-field
            v-model:value="variantSuffixValue2"
            name="variant-suffix-underlined"
            label="下划线样式"
            variant="underlined"
            suffix="°C"
            placeholder="温度"
          />
          <sp-input-field
            v-model:value="variantSuffixValue2"
            name="variant-suffix-unborder"
            label="无边框样式"
            variant="unborder"
            suffix="px"
            placeholder="尺寸"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>状态演示：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="statusSuffixValue2"
            name="status-suffix-error"
            label="错误状态"
            suffix="!"
            error
            placeholder="错误信息"
          />
          <sp-input-field
            v-model:value="statusSuffixValue2"
            name="status-suffix-success"
            label="成功状态"
            suffix="✓"
            success
            placeholder="成功信息"
          />
          <sp-input-field
            v-model:value="statusSuffixValue2"
            name="status-suffix-disabled"
            label="禁用状态"
            suffix="×"
            disabled
            placeholder="禁用信息"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>自定义后缀插槽：</label>
        <sp-input-field
          v-model:value="customSuffixValue"
          name="custom-suffix"
          label="自定义后缀"
          placeholder="使用自定义后缀内容"
        >
          <template #suffix="slotProps">
            <div class="custom-suffix">
              <span class="suffix-text">MB</span>
              <sp-icon
                name="Info"
                :size="16"
                color="#409eff"
              />
            </div>
          </template>
        </sp-input-field>
        <span class="value-display">使用插槽自定义后缀内容（文字+图标）</span>
      </div>
      <div class="demo-item">
        <label>填充变体前缀+后缀组合：</label>
        <sp-input-field
          v-model:value="filledPrefixSuffixValue"
          name="filled-prefix-suffix"
          label="价格区间"
          variant="filled"
          prefix="¥"
          suffix=".00"
          type="number"
          placeholder="输入价格"
        />
        <span class="value-display">
          填充变体：前缀 ¥ + 后缀 .00，测试 padding 调整
        </span>
      </div>
    </div>

    <div class="demo-section">
      <h2>📱 短信验证码演示</h2>
      <div class="demo-item">
        <label>基础短信验证码：</label>
        <sp-input-field
          v-model:value="smsCodeValue"
          type="smscode"
          name="sms-code-basic"
          label="短信验证码"
          placeholder="请输入收到的验证码"
          :maxlength="6"
          @sms-send="handleSendSmsCode"
          @sms-start="handleSmsCountdownStart"
          @sms-tick="handleSmsCountdownTick"
          @sms-end="handleSmsCountdownEnd"
        />
        <span class="value-display">
          💡 使用 type="smscode" 自动显示获取验证码按钮，支持60秒倒计时
        </span>
      </div>
      <div class="demo-item">
        <label>自定义倒计时2：</label>
        <sp-input-field
          v-model:value="smsCodeCustomValue"
          type="smscode"
          name="sms-code-custom"
          label="手机验证码"
          placeholder="请输入验证码"
          :maxlength="6"
          :sms-countdown="30"
          sms-send-text="发送验证码"
          sms-resend-text="重新发送"
          @sms-send="handleSendSmsCode"
          @sms-start="handleSmsCountdownStart"
          @sms-tick="handleSmsCountdownTick"
          @sms-end="handleSmsCountdownEnd"
        />
        <span class="value-display">自定义30秒倒计时和按钮文字</span>
      </div>
      <div class="demo-item">
        <label>不同变体：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="smsCodeVariantValue"
            type="smscode"
            name="sms-code-filled"
            label="填充样式"
            variant="filled"
            placeholder="填充样式 + 验证码"
            :maxlength="6"
            @sms-send="handleSendSmsCode"
            @sms-start="handleSmsCountdownStart"
            @sms-tick="handleSmsCountdownTick"
            @sms-end="handleSmsCountdownEnd"
          />
          <sp-input-field
            v-model:value="smsCodeVariantValue"
            type="smscode"
            name="sms-code-underlined"
            label="下划线样式"
            variant="underlined"
            placeholder="下划线样式 + 验证码"
            :maxlength="6"
            @sms-send="handleSendSmsCode"
            @sms-start="handleSmsCountdownStart"
            @sms-tick="handleSmsCountdownTick"
            @sms-end="handleSmsCountdownEnd"
          />
          <sp-input-field
            v-model:value="smsCodeVariantValue"
            type="smscode"
            name="sms-code-unborder"
            label="无边框样式"
            variant="unborder"
            placeholder="无边框样式 + 验证码"
            :maxlength="6"
            @sms-send="handleSendSmsCode"
            @sms-start="handleSmsCountdownStart"
            @sms-tick="handleSmsCountdownTick"
            @sms-end="handleSmsCountdownEnd"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>状态演示：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="smsCodeStatusValue"
            type="smscode"
            name="sms-code-error"
            label="错误状态"
            error
            placeholder="验证码错误"
            :maxlength="6"
            @sms-send="handleSendSmsCode"
            @sms-start="handleSmsCountdownStart"
            @sms-tick="handleSmsCountdownTick"
            @sms-end="handleSmsCountdownEnd"
          />
          <sp-input-field
            v-model:value="smsCodeStatusValue"
            type="smscode"
            name="sms-code-success"
            label="成功状态"
            success
            placeholder="验证码正确"
            :maxlength="6"
            @sms-send="handleSendSmsCode"
            @sms-start="handleSmsCountdownStart"
            @sms-tick="handleSmsCountdownTick"
            @sms-end="handleSmsCountdownEnd"
          />
          <sp-input-field
            v-model:value="smsCodeStatusValue"
            type="smscode"
            name="sms-code-disabled"
            label="禁用状态"
            disabled
            placeholder="禁用状态"
            :maxlength="6"
            @sms-send="handleSendSmsCode"
            @sms-start="handleSmsCountdownStart"
            @sms-tick="handleSmsCountdownTick"
            @sms-end="handleSmsCountdownEnd"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>实际应用场景：</label>
        <sp-input-field
          v-model:value="smsCodeValue"
          name="sms-code-practical"
          label="手机号码"
          type="tel"
          placeholder="请输入手机号码"
        />
        <br style="margin: 10px 0" />
        <sp-input-field
          v-model:value="smsCodeValue"
          type="smscode"
          name="sms-code-verification"
          label="验证码"
          placeholder="请输入收到的6位验证码"
          :maxlength="6"
          :rules="'required|min:6'"
          @sms-send="handleSendSmsCode"
          @sms-start="handleSmsCountdownStart"
          @sms-tick="handleSmsCountdownTick"
          @sms-end="handleSmsCountdownEnd"
        />
        <span class="value-display">
          典型的手机注册/登录场景：先输入手机号，再获取验证码
        </span>
      </div>
    </div>

    <div class="demo-section">
      <h2>📞 电话号码输入演示（基于 libphonenumber-js）</h2>
      <div class="demo-item">
        <label>基础电话输入：</label>
        <sp-input-field
          v-model:value="telValue"
          name="tel-basic"
          label="电话号码"
          type="tel"
          placeholder="请输入电话号码"
        />
        <span class="value-display">
          type="tel" 自动显示电话图标和国家代码选择，基于 Google
          libphonenumber-js 库
        </span>
        <div
          class="value-display"
          v-if="telValue"
        >
          <strong>当前输入值：</strong>
          {{ telValue }}
        </div>
      </div>
      <div class="demo-item">
        <label>不同变体：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="telVariantValue"
            name="tel-filled"
            label="填充样式"
            variant="filled"
            type="tel"
            placeholder="电话号码"
          />
          <sp-input-field
            v-model:value="telVariantValue"
            name="tel-underlined"
            label="下划线样式"
            variant="underlined"
            type="tel"
            placeholder="电话号码"
          />
          <sp-input-field
            v-model:value="telVariantValue"
            name="tel-unborder"
            label="无边框样式"
            variant="unborder"
            type="tel"
            placeholder="电话号码"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>状态演示：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="telStatusValue"
            name="tel-error"
            label="错误状态"
            type="tel"
            error
            placeholder="错误的电话号码"
          />
          <sp-input-field
            v-model:value="telStatusValue"
            name="tel-success"
            label="成功状态"
            type="tel"
            success
            placeholder="正确的电话号码"
          />
          <sp-input-field
            v-model:value="telStatusValue"
            name="tel-disabled"
            label="禁用状态"
            type="tel"
            disabled
            placeholder="禁用的电话号码"
          />
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>🎯 外部前缀图标演示</h2>
      <div class="demo-item">
        <label>外部前缀图标：</label>
        <sp-input-field
          v-model:value="outerPrefixValue"
          name="outer-prepend"
          label="用户名"
          prepend-icon="Person"
          placeholder="外部图标 + 内部标签"
        />
        <span class="value-display">图标位于输入框外部左侧</span>
      </div>
      <div class="demo-item">
        <label>内部前缀图标：</label>
        <sp-input-field
          v-model:value="innerPrefixValue"
          name="inner-prepend"
          label="邮箱地址"
          prepend-icon-inner="Mail"
          placeholder="内部图标 + 浮动标签"
        />
        <span class="value-display">图标位于输入框内部左侧</span>
      </div>
      <div class="demo-item">
        <label>双前缀图标：</label>
        <sp-input-field
          v-model:value="doublePrefixValue"
          name="double-prepend"
          label="高级搜索"
          prepend-icon="Search"
          prepend-icon-inner="Settings"
          placeholder="外部 + 内部双图标"
        />
        <span class="value-display">外部搜索图标，内部设置图标</span>
      </div>
      <div class="demo-item">
        <label>不同变体：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="variantPrefixValue"
            name="variant-filled"
            label="填充样式"
            variant="filled"
            prepend-icon="Person"
            prepend-icon-inner="Mail"
            placeholder="填充样式 + 双图标"
          />
          <sp-input-field
            v-model:value="variantPrefixValue"
            name="variant-underlined"
            label="下划线样式"
            variant="underlined"
            prepend-icon="Search"
            prepend-icon-inner="Settings"
            placeholder="下划线样式 + 双图标"
          />
          <sp-input-field
            v-model:value="variantPrefixValue"
            name="variant-unborder"
            label="无边框样式"
            variant="unborder"
            prepend-icon="Person"
            prepend-icon-inner="Mail"
            placeholder="无边框样式 + 双图标"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>响应式尺寸：</label>
        <div class="responsive-showcase">
          <sp-input-field
            v-model:value="responsivePrefixValue"
            name="responsive-small"
            label="小尺寸"
            size="small"
            prepend-icon="Person"
            prepend-icon-inner="Mail"
            placeholder="小尺寸双图标"
          />
          <sp-input-field
            v-model:value="responsivePrefixValue"
            name="responsive-medium"
            label="中等尺寸"
            size="medium"
            prepend-icon="Person"
            prepend-icon-inner="Mail"
            placeholder="中等尺寸双图标"
          />
          <sp-input-field
            v-model:value="responsivePrefixValue"
            name="responsive-large"
            label="大尺寸"
            size="large"
            prepend-icon="Person"
            prepend-icon-inner="Mail"
            placeholder="大尺寸双图标"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>向后兼容性：</label>
        <sp-input-field
          v-model:value="compatibilityValue"
          name="compatibility"
          label="传统用法"
          prepend-icon="Mail"
          placeholder="只使用 prepend-icon（内部显示）"
        />
        <span class="value-display">
          使用旧的 prepend-icon 属性，图标在内部显示
        </span>
      </div>
    </div>

    <div class="demo-section">
      <h2>🎯 外部后缀图标演示</h2>
      <div class="demo-item">
        <label>外部后缀图标：</label>
        <sp-input-field
          v-model:value="outerSuffixValue"
          name="outer-append"
          label="系统设置"
          append-icon="Settings"
          placeholder="外部图标 + 内部标签"
        />
        <span class="value-display">图标位于输入框外部右侧</span>
      </div>
      <div class="demo-item">
        <label>内部后缀图标：</label>
        <sp-input-field
          v-model:value="innerSuffixValue"
          name="inner-append"
          label="清除内容"
          append-icon-inner="Settings"
          placeholder="内部图标 + 浮动标签"
        />
        <span class="value-display">图标位于输入框内部右侧</span>
      </div>
      <div class="demo-item">
        <label>双后缀图标：</label>
        <sp-input-field
          v-model:value="doubleSuffixValue"
          name="double-append"
          label="高级操作"
          append-icon="Settings"
          append-icon-inner="Mail"
          placeholder="外部 + 内部双图标"
        />
        <span class="value-display">外部设置图标，内部清除图标</span>
      </div>
      <div class="demo-item">
        <label>前缀 + 后缀组合：</label>
        <sp-input-field
          v-model:value="prefixSuffixComboValue"
          name="prepend-append-combo"
          label="完整图标组合"
          prepend-icon="Search"
          prepend-icon-inner="Mail"
          append-icon="Settings"
          append-icon-inner="Search"
          placeholder="前缀 + 后缀完整组合"
        />
        <span class="value-display">
          外部搜索，内部邮件，外部设置，内部清除
        </span>
      </div>
      <div class="demo-item">
        <label>不同变体：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="variantSuffixValue"
            name="variant-append-filled"
            label="填充样式"
            variant="filled"
            append-icon="Settings"
            append-icon-inner="Clear"
            placeholder="填充样式 + 双后缀图标"
          />
          <sp-input-field
            v-model:value="variantSuffixValue"
            name="variant-append-underlined"
            label="下划线样式"
            variant="underlined"
            append-icon="Settings"
            append-icon-inner="Clear"
            placeholder="下划线样式 + 双后缀图标"
          />
          <sp-input-field
            v-model:value="variantSuffixValue"
            name="variant-append-unborder"
            label="无边框样式"
            variant="unborder"
            append-icon="Settings"
            append-icon-inner="Clear"
            placeholder="无边框样式 + 双后缀图标"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>响应式尺寸：</label>
        <div class="responsive-showcase">
          <sp-input-field
            v-model:value="responsiveSuffixValue"
            name="responsive-append-small"
            label="小尺寸"
            size="small"
            append-icon="Settings"
            append-icon-inner="Clear"
            placeholder="小尺寸双后缀图标"
          />
          <sp-input-field
            v-model:value="responsiveSuffixValue"
            name="responsive-append-medium"
            label="中等尺寸"
            size="medium"
            append-icon="Settings"
            append-icon-inner="Clear"
            placeholder="中等尺寸双后缀图标"
          />
          <sp-input-field
            v-model:value="responsiveSuffixValue"
            name="responsive-append-large"
            label="大尺寸"
            size="large"
            append-icon="Settings"
            append-icon-inner="Clear"
            placeholder="大尺寸双后缀图标"
          />
        </div>
      </div>
      <div class="demo-item">
        <label>向后兼容性：</label>
        <sp-input-field
          v-model:value="compatibilitySuffixValue"
          name="compatibility-append"
          label="传统用法"
          append-icon="Settings"
          placeholder="只使用 append-icon（内部显示）"
        />
        <span class="value-display">
          使用旧的 append-icon 属性，图标在内部显示
        </span>
      </div>
      <div class="demo-item">
        <label>状态测试：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="stateSuffixValue"
            name="state-append-error"
            label="错误状态"
            append-icon="Settings"
            append-icon-inner="Clear"
            error
            placeholder="错误状态双后缀图标"
          />
          <sp-input-field
            v-model:value="stateSuffixValue"
            name="state-append-success"
            label="成功状态"
            append-icon="Settings"
            append-icon-inner="Clear"
            success
            placeholder="成功状态双后缀图标"
          />
          <sp-input-field
            v-model:value="stateSuffixValue"
            name="state-append-disabled"
            label="禁用状态"
            append-icon="Settings"
            append-icon-inner="Clear"
            disabled
            placeholder="禁用状态双后缀图标"
          />
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>🔍 搜索控件演示</h2>
      <div class="demo-item">
        <label>基础搜索：</label>
        <sp-input-field
          v-model:value="basicSearchValue"
          name="basic-search"
          label="搜索内容"
          type="search"
          placeholder="输入搜索关键词"
          @search="handleBasicSearch"
        />
        <span class="value-display">
          💡 智能搜索：使用 type="search"
          启用搜索功能，有内容时点击搜索图标会显示 loading
        </span>
      </div>
      <div class="demo-item">
        <label>智能搜索：</label>
        <sp-input-field
          v-model:value="smartSearchValue"
          name="smart-search"
          label="智能搜索"
          type="search"
          placeholder="支持实时搜索和快速清除"
          @search="handleSmartSearch"
          @input="handleSearchInput"
          @clear="clearSmartSearch"
        />
        <span class="value-display">
          实时搜索：{{ searchResults.length }}
          条结果（点击搜索需要有内容才会显示 loading）
        </span>
      </div>
      <div class="demo-item">
        <label>高级搜索：</label>
        <sp-input-field
          v-model:value="advancedSearchValue"
          name="advanced-search"
          label="高级搜索"
          placeholder="支持搜索历史和建议"
          prepend-icon="Search"
          append-icon="Settings"
          clearable
          @click:prepend="handleAdvancedSearch"
          @click:append="openSearchSettings"
          @keyup.enter="handleAdvancedSearch"
        />
        <span class="value-display">按回车键或点击搜索图标执行搜索</span>
      </div>
      <div class="demo-item">
        <label>搜索状态：</label>
        <sp-input-field
          v-model:value="searchStatusValue"
          name="search-status"
          label="搜索状态演示"
          type="search"
          placeholder="有内容时点击搜索会自动显示 loading"
          @search="handleSearchWithLoading"
        />
        <span class="value-display">
          ✨ 自动 loading：有内容时点击搜索自动显示 2 秒 loading 状态
        </span>
      </div>
      <div class="demo-item">
        <label>手动 loading 对比：</label>
        <sp-input-field
          v-model:value="manualLoadingValue"
          name="manual-loading"
          label="手动 loading 演示"
          type="search"
          placeholder="手动控制的 loading 状态"
          :loading="manualLoading"
          @search="handleManualLoadingSearch"
        />
        <div class="button-group">
          <button @click="toggleManualLoading">
            {{ manualLoading ? '停止 loading' : '开始 loading' }}
          </button>
        </div>
        <span class="value-display">
          对比：手动 loading 会禁用搜索控件，自动搜索 loading 不会
        </span>
      </div>
      <div class="demo-item">
        <label>搜索历史：</label>
        <sp-input-field
          v-model:value="historySearchValue"
          name="history-search"
          label="搜索历史"
          type="search"
          placeholder="搜索功能 + 历史图标"
          append-icon="History"
          @search="handleHistorySearch"
          @click:append="showSearchHistory"
          @focus="showSearchSuggestions"
        />
        <span class="value-display">
          搜索历史：{{ searchHistory.slice(0, 3).join(', ')
          }}{{ searchHistory.length > 3 ? '...' : '' }}
        </span>
      </div>
      <div class="demo-item">
        <label>混合功能搜索：</label>
        <sp-input-field
          v-model:value="mixedSearchValue"
          name="mixed-search"
          label="混合功能搜索"
          type="search"
          placeholder="搜索控件 + 前缀图标 + 字数限制"
          prepend-icon="Mail"
          :maxlength="30"
          show-word-limit
          @search="handleMixedSearch"
          @clear="clearMixedSearch"
        />
        <span class="value-display">展示搜索控件与其他功能的组合使用</span>
      </div>
      <div class="demo-item">
        <label>变体样式搜索：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="variantSearchValue"
            name="variant-search-filled"
            label="填充样式搜索"
            type="search"
            variant="filled"
            placeholder="填充样式的搜索框"
            @search="handleVariantSearch"
          />
          <sp-input-field
            v-model:value="variantSearchValue"
            name="variant-search-underlined"
            label="下划线样式搜索"
            type="search"
            variant="underlined"
            placeholder="下划线样式的搜索框"
            @search="handleVariantSearch"
          />
          <sp-input-field
            v-model:value="variantSearchValue"
            name="variant-search-unborder"
            label="无边框样式搜索"
            type="search"
            variant="unborder"
            placeholder="无边框样式的搜索框"
            @search="handleVariantSearch"
          />
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>🔧 功能组合</h2>
      <div class="demo-item">
        <label>密码输入：</label>
        <sp-input-field
          v-model:value="passwordValue"
          name="password"
          label="登录密码"
          type="password"
          show-password
          placeholder="请输入密码"
        />
      </div>
      <div class="demo-item">
        <label>数字输入（自定义箭头）</label>
        <sp-input-field
          v-model:value="numberValue"
          label="数量"
          type="number"
          :min="0"
          :max="100"
          :step="1"
          placeholder="请输入数字 (0-100)"
        />
        <span class="value-display">当前值: {{ numberValue || 0 }}</span>
      </div>
      <div class="demo-item">
        <label>小数数字输入</label>
        <sp-input-field
          v-model:value="decimalValue"
          label="价格"
          type="number"
          :min="0"
          :step="0.01"
          clearable
          placeholder="请输入价格"
        />
        <span class="value-display">当前值: {{ decimalValue || 0 }}</span>
      </div>
      <div class="demo-item">
        <label>可清除：</label>
        <sp-input-field
          v-model:value="clearableValue"
          name="clearable"
          label="搜索关键词"
          clearable
          placeholder="输入搜索内容"
        />
      </div>
      <div class="demo-item">
        <label>字数限制：</label>
        <sp-input-field
          v-model:value="limitValue"
          name="limit"
          label="个人简介"
          :maxlength="50"
          show-word-limit
          placeholder="最多50个字符"
        />
      </div>
      <div class="demo-item">
        <label>前缀图标：</label>
        <sp-input-field
          v-model:value="prefixIconValue"
          name="prepend"
          label="邮箱地址"
          prepend-icon-inner="Mail"
          placeholder="<EMAIL>"
        />
      </div>
      <div class="demo-item">
        <label>后缀图标：</label>
        <sp-input-field
          v-model:value="suffixIconValue"
          name="append"
          label="用户设置"
          append-icon="Settings"
          placeholder="配置用户信息"
        />
      </div>
      <div class="demo-item">
        <label>完整组合：</label>
        <sp-input-field
          v-model:value="fullFeatureValue"
          name="full"
          label="完整功能演示"
          variant="filled"
          effect="glow"
          size="large"
          prepend-icon="Person"
          prepend-icon-inner="Mail"
          clearable
          :maxlength="30"
          show-word-limit
          placeholder="所有功能组合使用"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🔄 加载状态</h2>
      <div class="demo-item">
        <label>加载中：</label>
        <sp-input-field
          v-model:value="loadingValue"
          name="loading"
          label="数据加载中"
          loading
          placeholder="加载状态的输入框"
        />
        <span class="value-display">loading 状态下自动禁用</span>
      </div>
      <div class="demo-item">
        <label>动态控制：</label>
        <sp-input-field
          v-model:value="toggleLoadingValue"
          name="toggle-loading"
          label="可控制加载"
          :loading="isToggleLoading"
          placeholder="可以控制的加载状态"
        />
        <div class="button-group">
          <button @click="toggleLoading">
            {{ isToggleLoading ? '停止加载' : '开始加载' }}
          </button>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>⚠️ 状态演示</h2>
      <div class="demo-item">
        <label>正常状态：</label>
        <sp-input-field
          v-model:value="normalStateValue"
          name="normal"
          label="正常输入框"
          placeholder="正常状态"
        />
      </div>
      <div class="demo-item">
        <label>禁用状态：</label>
        <sp-input-field
          v-model:value="disabledStateValue"
          name="disabled"
          label="禁用输入框"
          disabled
          placeholder="禁用状态"
        />
      </div>
      <div class="demo-item">
        <label>只读状态：</label>
        <sp-input-field
          v-model:value="readonlyStateValue"
          name="readonly"
          label="只读输入框"
          readonly
          placeholder="只读状态"
        />
      </div>
      <div class="demo-item">
        <label>错误状态：</label>
        <sp-input-field
          v-model:value="errorStateValue"
          name="error"
          label="错误输入框"
          error
          placeholder="错误状态"
        />
      </div>
      <div class="demo-item">
        <label>警告状态：</label>
        <sp-input-field
          v-model:value="warningStateValue"
          label="警告输入框"
          warning
        />
      </div>
      <div class="demo-item">
        <label>成功状态：</label>
        <sp-input-field
          v-model:value="successStateValue"
          label="成功输入框"
          placeholder="成功状态"
          success
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🧪 表单验证演示</h2>
      <div class="demo-item">
        <label>邮箱验证：</label>
        <sp-input-field
          v-model:value="emailValidateValue"
          name="email-validate"
          label="邮箱地址"
          :rules="emailValidationRules"
          placeholder="输入邮箱进行验证"
        />
        <span class="value-display">失去焦点时自动验证</span>
      </div>
      <div class="demo-item">
        <label>密码强度：</label>
        <sp-input-field
          v-model:value="passwordValidateValue"
          name="password-validate"
          label="设置密码"
          type="password"
          :rules="passwordValidationRules"
          show-password
          placeholder="至少8位，包含数字和字母"
        />
      </div>
      <div class="demo-item">
        <label>手机号码：</label>
        <sp-input-field
          v-model:value="phoneValidateValue"
          name="phone-validate"
          label="手机号码"
          :rules="phoneValidationRules"
          placeholder="请输入11位手机号"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🎯 方法调用</h2>
      <div class="demo-item">
        <label>方法测试：</label>
        <sp-input-field
          ref="methodFieldRef"
          v-model:value="methodValue"
          name="method"
          label="方法测试"
          placeholder="测试组件方法"
        />
        <div class="button-group">
          <button @click="focusField">聚焦</button>
          <button @click="blurField">失焦</button>
          <button @click="selectField">选中</button>
          <button @click="clearField">清空</button>
          <button @click="validateField">验证</button>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>📝 插槽演示</h2>
      <div class="demo-item">
        <label>内部前缀插槽：</label>
        <sp-input-field
          v-model:value="prefixSlotValue"
          name="prepend-slot"
          label="前缀插槽"
          placeholder="自定义前缀内容"
        >
          <template #prepend>
            <sp-icon
              name="Search"
              :size="16"
              color="#409eff"
            />
          </template>
        </sp-input-field>
      </div>
      <div class="demo-item">
        <label>内部后缀插槽：</label>
        <sp-input-field
          v-model:value="suffixSlotValue"
          name="append-slot"
          label="后缀插槽"
          placeholder="自定义后缀内容"
        >
          <template #append>
            <span class="append-text">点击获取验证码</span>
          </template>
        </sp-input-field>
      </div>
      <div class="demo-item">
        <label>后缀文字演示：</label>
        <sp-input-field
          v-model:value="appendTextValue"
          name="append-text"
          label="后缀文字"
          placeholder="演示后缀插槽中的文字"
        >
          <template #append>
            <span class="append-text">MB/s</span>
          </template>
        </sp-input-field>
        <span class="value-display">使用插槽传入纯文字内容</span>
      </div>
      <div class="demo-item">
        <label>混合内容后缀：</label>
        <sp-input-field
          v-model:value="appendMixedValue"
          name="append-mixed"
          label="混合后缀"
          placeholder="文字 + 图标组合"
        >
          <template #append>
            <span class="append-text">元</span>
            <sp-icon
              name="Settings"
              :size="16"
              color="#409eff"
              clickable
              @click="handleMixedAppendClick"
            />
          </template>
        </sp-input-field>
        <span class="value-display">文字 + 图标的组合使用</span>
      </div>
      <div class="demo-item">
        <label>不同变体文字后缀：</label>
        <div class="variant-showcase">
          <sp-input-field
            v-model:value="variantTextAppendValue"
            name="variant-text-filled"
            label="填充样式"
            variant="filled"
            placeholder="填充样式 + 文字后缀"
          >
            <template #append>
              <span class="append-text">kg</span>
            </template>
          </sp-input-field>
          <sp-input-field
            v-model:value="variantTextAppendValue"
            name="variant-text-underlined"
            label="下划线样式"
            variant="underlined"
            placeholder="下划线样式 + 文字后缀"
          >
            <template #append>
              <span class="append-text">%</span>
            </template>
          </sp-input-field>
          <sp-input-field
            v-model:value="variantTextAppendValue"
            name="variant-text-unborder"
            label="无边框样式"
            variant="unborder"
            placeholder="无边框样式 + 文字后缀"
          >
            <template #append>
              <span class="append-text">GB</span>
            </template>
          </sp-input-field>
        </div>
      </div>
      <div class="demo-item">
        <label>外部前缀插槽：</label>
        <sp-input-field
          v-model:value="prefixOuterSlotValue"
          name="prepend-outer-slot"
          label="外部前缀插槽"
          placeholder="自定义外部前缀内容"
        >
          <template #prefixOuter="slotProps">
            <div class="custom-prepend-outer">
              <sp-icon
                name="User"
                :size="slotProps.iconSize"
                color="#e74c3c"
              />
              <span class="custom-text">用户</span>
            </div>
          </template>
        </sp-input-field>
      </div>
      <div class="demo-item">
        <label>外部后缀插槽：</label>
        <sp-input-field
          v-model:value="suffixOuterSlotValue"
          name="append-outer-slot"
          label="外部后缀插槽"
          placeholder="自定义外部后缀内容"
        >
          <template #suffixOuter="slotProps">
            <div class="custom-append-outer">
              <button
                class="custom-btn"
                @click="handleCustomAction"
              >
                <sp-icon
                  name="Settings"
                  :size="slotProps.iconSize"
                />
                设置
              </button>
            </div>
          </template>
        </sp-input-field>
      </div>
      <div class="demo-item">
        <label>完整插槽组合：</label>
        <sp-input-field
          v-model:value="fullSlotValue"
          name="full-slot"
          label="完整插槽演示"
          placeholder="所有插槽组合使用"
        >
          <!-- 外部前缀插槽 -->
          <template #prefixOuter="slotProps">
            <div class="custom-prepend-outer">
              <sp-icon
                name="Mail"
                :size="slotProps.iconSize"
                color="#3498db"
              />
            </div>
          </template>

          <!-- 内部前缀插槽 -->
          <template #prepend>
            <sp-icon
              name="Search"
              :size="16"
              color="#2ecc71"
            />
          </template>

          <!-- 内部后缀插槽 -->
          <template #append>
            <sp-icon
              name="Search"
              :size="16"
              color="#e74c3c"
            />
          </template>

          <!-- 外部后缀插槽 -->
          <template #suffixOuter="slotProps">
            <div class="custom-append-outer">
              <button
                class="custom-btn"
                @click="handleFullSlotAction"
              >
                <sp-icon
                  name="Settings"
                  :size="slotProps.iconSize"
                />
              </button>
            </div>
          </template>
        </sp-input-field>
        <span class="value-display">
          4个插槽同时使用：外部前缀、内部前缀、内部后缀、外部后缀
        </span>
      </div>
    </div>

    <div class="demo-section">
      <h2>📋 API 使用说明</h2>
      <div class="api-info">
        <h3>🌟 新增特性</h3>
        <ul>
          <li>
            <code>label</code>
            - 浮动标签文本，点击时自动聚焦到输入框
          </li>
          <li>
            <code>name</code>
            - 表单字段名称，用于表单验证和标识
          </li>
          <li>
            <code>required</code>
            - 是否必填，显示红色星号
          </li>
          <li>
            <code>rules</code>
            - 验证规则，支持 VeeValidate 规则
          </li>
          <li>
            <code>helper-text</code>
            - 帮助文本，在无验证错误时显示
          </li>
          <li>
            <code>persistent-label</code>
            - 标签是否始终浮动
          </li>
          <li>
            <code>show-message</code>
            - 是否显示验证消息
          </li>
          <li>
            <code>type="search"</code>
            - 启用搜索功能，显示搜索和清除按钮
          </li>
          <li>
            <code>prepend-icon</code>
            - 外部前缀图标，位于输入框外部左侧
          </li>
          <li>
            <code>prepend-icon-inner</code>
            - 内部前缀图标，位于输入框内部左侧
          </li>
          <li>
            <code>append-icon-inner</code>
            - 内部后缀图标，位于输入框内部右侧
          </li>
          <li>
            <code>@click:prepend</code>
            - 外部前缀图标点击事件
          </li>
          <li>
            <code>@click:prepend-inner</code>
            - 内部前缀图标点击事件
          </li>
          <li>
            <code>@click:append</code>
            - 外部后缀图标点击事件
          </li>
          <li>
            <code>@click:append-inner</code>
            - 内部后缀图标点击事件
          </li>
        </ul>

        <h3>🎯 浮动标签行为</h3>
        <ul>
          <li>默认标签位于输入框内部</li>
          <li>聚焦、有值或有 placeholder 时自动上浮</li>
          <li>上浮时标签缩小并改变颜色</li>
          <li>点击标签会自动聚焦到输入框</li>
          <li>支持所有变体样式的适配</li>
        </ul>

        <h3>📋 表单集成</h3>
        <ul>
          <li>
            可直接作为
            <code>sp-form</code>
            的子组件使用
          </li>
          <li>
            无需包装
            <code>sp-form-item</code>
          </li>
          <li>自动集成 VeeValidate 验证</li>
          <li>支持实时验证和失焦验证</li>
          <li>错误消息自动显示和隐藏</li>
        </ul>

        <h3>🎨 主题系统</h3>
        <ul>
          <li>
            <strong>预设主题</strong>
            ：blue, green, red, orange, purple, pink, cyan, gray
          </li>
          <li>
            <strong>自定义主题</strong>
            ：支持任意颜色值，自动生成色系
          </li>
          <li>
            <strong>全局配置</strong>
            ：在 main.ts 中使用
            <code>setSpeedUIThemeConfig</code>
            配置
          </li>
          <li>
            <strong>动态切换</strong>
            ：使用
            <code>applyTheme</code>
            动态切换主题
          </li>
          <li>
            <strong>自定义映射</strong>
            ：支持自定义颜色算法函数
          </li>
        </ul>

        <h3>🔧 主题配置示例</h3>
        <div class="code-example">
          <pre><code>// main.ts 中配置
import { setSpeedUIThemeConfig, applyTheme } from '@speed-ui/ui'

setSpeedUIThemeConfig({
  // 自定义绿色主题
  green: {
    primary: '#10b981',
    primaryHover: '#34d399',
    primaryActive: '#059669',
    // ... 其他颜色
  },
  
  // 自定义颜色算法
  customThemeMapper: (color) => generateTheme(color)
})

// 应用主题
applyTheme('green')        // 预设主题
applyTheme('#ff6b6b')      // 自定义颜色</code></pre>
        </div>

        <h3>🔄 继承功能</h3>
        <ul>
          <li>
            完全继承
            <code>sp-input</code>
            的所有功能
          </li>
          <li>
            支持所有变体：default, underlined, filled, ghost, pill, square,
            unborder
          </li>
          <li>支持所有尺寸：small, medium, large</li>
          <li>支持发光效果：effect="glow"</li>
          <li>支持所有功能：密码切换、清除、字数统计、图标、加载等</li>
        </ul>

        <h3>⚡ 方法调用</h3>
        <ul>
          <li>
            <code>focus()</code>
            - 聚焦输入框
          </li>
          <li>
            <code>blur()</code>
            - 失焦输入框
          </li>
          <li>
            <code>select()</code>
            - 选中文本
          </li>
          <li>
            <code>clear()</code>
            - 清空内容
          </li>
          <li>
            <code>validate()</code>
            - 手动验证
          </li>
          <li>
            <code>resetField()</code>
            - 重置字段
          </li>
          <li>
            <code>clearValidate()</code>
            - 清除验证状态
          </li>
        </ul>

        <h3>🎯 双前缀图标系统</h3>
        <ul>
          <li>
            <strong>外部前缀图标</strong>
            ：使用
            <code>prepend-icon</code>
            属性，图标位于输入框外部左侧，独立显示
          </li>
          <li>
            <strong>内部前缀图标</strong>
            ：使用
            <code>prepend-icon-inner</code>
            属性，图标位于输入框内部左侧
          </li>
          <li>
            <strong>向后兼容</strong>
            ：如果只使用
            <code>prepend-icon</code>
            ，图标会显示在内部（保持旧版本行为）
          </li>
          <li>
            <strong>组合使用</strong>
            ：可以同时使用两种前缀图标，创建丰富的视觉层次
          </li>
          <li>
            <strong>响应式设计</strong>
            ：图标大小会根据组件尺寸自动调整
          </li>
          <li>
            <strong>状态响应</strong>
            ：图标颜色会根据组件状态（聚焦、错误、成功等）自动变化
          </li>
        </ul>

        <h3>🎯 双后缀图标系统</h3>
        <ul>
          <li>
            <strong>外部后缀图标</strong>
            ：使用
            <code>append-icon</code>
            属性，图标位于输入框外部右侧，独立显示
          </li>
          <li>
            <strong>内部后缀图标</strong>
            ：使用
            <code>append-icon-inner</code>
            属性，图标位于输入框内部右侧
          </li>
          <li>
            <strong>向后兼容</strong>
            ：如果只使用
            <code>append-icon</code>
            ，图标会显示在内部（保持旧版本行为）
          </li>
          <li>
            <strong>组合使用</strong>
            ：可以同时使用两种后缀图标，创建丰富的视觉层次
          </li>
          <li>
            <strong>响应式设计</strong>
            ：图标大小会根据组件尺寸自动调整
          </li>
          <li>
            <strong>状态响应</strong>
            ：图标颜色会根据组件状态（聚焦、错误、成功等）自动变化
          </li>
          <li>
            <strong>插槽支持</strong>
            ：外部前缀和后缀区域都支持插槽，可以插入任何自定义内容
          </li>
        </ul>

        <h3>📱 短信验证码组件</h3>
        <ul>
          <li>
            <strong>SmsCodeControls 组件</strong>
            ：专门用于短信验证码场景的控制器
          </li>
          <li>
            <strong>倒计时功能</strong>
            ：点击后自动开始倒计时，防止频繁发送
          </li>
          <li>
            <strong>自定义倒计时</strong>
            ：支持自定义倒计时时长（默认60秒）
          </li>
          <li>
            <strong>按钮文字</strong>
            ：支持自定义发送和重新获取按钮文字
          </li>
          <li>
            <strong>状态管理</strong>
            ：自动管理发送状态，倒计时期间禁用点击
          </li>
          <li>
            <strong>事件回调</strong>
            ：提供发送、倒计时开始、进行中、结束等事件
          </li>
          <li>
            <strong>外部控制</strong>
            ：暴露方法支持外部开始/停止倒计时
          </li>
          <li>
            <strong>样式适配</strong>
            ：自动适配主题色和组件状态
          </li>
        </ul>

        <h3>📱 短信验证码使用示例</h3>
        <div class="code-example">
          <pre><code>// 基础用法
&lt;sp-input-field
  v-model:value="smsCode"
  label="验证码"
  placeholder="请输入验证码"
  :maxlength="6"
&gt;
  &lt;template #append&gt;
    &lt;sms-code-controls
      @send="handleSendSmsCode"
      @countdown-start="onCountdownStart"
      @countdown-tick="onCountdownTick"
      @countdown-end="onCountdownEnd"
    /&gt;
  &lt;/template&gt;
&lt;/sp-input-field&gt;

// 自定义配置
&lt;sms-code-controls
  :countdown="30"
  send-text="发送验证码"
  resend-text="重新发送"
  :can-send="!isDisabled"
  @send="handleSendSmsCode"
/&gt;</code></pre>
        </div>

        <h3>🎨 插槽系统</h3>
        <ul>
          <li>
            <strong>内部前缀插槽</strong>
            ：使用
            <code>#prepend</code>
            插槽，位于输入框内部左侧
          </li>
          <li>
            <strong>内部后缀插槽</strong>
            ：使用
            <code>#append</code>
            插槽，位于输入框内部右侧
          </li>
          <li>
            <strong>外部前缀插槽</strong>
            ：使用
            <code>#prefixOuter</code>
            插槽，位于输入框外部左侧
          </li>
          <li>
            <strong>外部后缀插槽</strong>
            ：使用
            <code>#suffixOuter</code>
            插槽，位于输入框外部右侧
          </li>
          <li>
            <strong>插槽属性</strong>
            ：所有插槽都提供丰富的属性，包括样式类、图标尺寸等
          </li>
          <li>
            <strong>灵活定制</strong>
            ：可以插入任何内容，包括按钮、文本、图标组合等
          </li>
        </ul>

        <h3>🖱️ 图标点击事件</h3>
        <ul>
          <li>
            <strong>@click:prepend</strong>
            ：外部前缀图标点击事件
          </li>
          <li>
            <strong>@click:prepend-inner</strong>
            ：内部前缀图标点击事件
          </li>
          <li>
            <strong>@click:append</strong>
            ：外部后缀图标点击事件
          </li>
          <li>
            <strong>@click:append-inner</strong>
            ：内部后缀图标点击事件
          </li>
          <li>
            <strong>自动禁用</strong>
            ：在禁用状态下，图标点击事件自动禁用
          </li>
          <li>
            <strong>事件参数</strong>
            ：所有点击事件都传递原生 MouseEvent 对象
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { applyTheme } from '../../../packages/ui/src/config/speedThemeConfig'

  // 基础功能
  const basicValue = ref('')

  // 动态尺寸测试
  const customSize = ref(48) // 默认尺寸
  const dynamicSizeValue = ref('')
  const size30Value = ref('')
  const size48Value = ref('')
  const size60Value = ref('')
  const size80Value = ref('')
  const requiredValue = ref('')
  const persistentValue = ref('')
  const helperValue = ref('')

  // 主题相关
  const currentTheme = ref('blue')
  const customThemeColor = ref('#1890ff')
  const themeShowcaseValue = ref('')

  // 可用主题
  const availableThemes = [
    { name: 'blue', label: '蓝色', color: '#1890ff' },
    { name: 'green', label: '绿色', color: '#10b981' },
    { name: 'red', label: '红色', color: '#ff4d4f' },
    { name: 'orange', label: '橙色', color: '#fa8c16' },
    { name: 'purple', label: '紫色', color: '#8b5cf6' },
    { name: 'pink', label: '粉色', color: '#ec4899' },
    { name: 'cyan', label: '青色', color: '#13c2c2' },
    { name: 'gray', label: '灰色', color: '#595959' },
  ]

  // 变体样式
  const defaultVariantValue = ref('')
  const underlinedVariantValue = ref('')
  const filledVariantValue = ref('')
  const squareVariantValue = ref('')
  const unborderVariantValue = ref('')

  // 发光效果
  const glowDefaultValue = ref('')
  const glowUnderlineValue = ref('')

  // 尺寸
  const smallSizeValue = ref('')
  const mediumSizeValue = ref('')
  const largeSizeValue = ref('')

  // 功能组合
  const passwordValue = ref('')
  const numberValue = ref(10)
  const decimalValue = ref(9.99)
  const clearableValue = ref('可以清除这段文字')
  const limitValue = ref('')
  const prefixIconValue = ref('')
  const suffixIconValue = ref('')
  const fullFeatureValue = ref('')

  // 加载状态
  const loadingValue = ref('')
  const toggleLoadingValue = ref('')
  const isToggleLoading = ref(false)

  // 状态演示
  const normalStateValue = ref('正常状态示例')
  const disabledStateValue = ref('禁用状态示例')
  const readonlyStateValue = ref('只读状态示例')
  const errorStateValue = ref('')
  const warningStateValue = ref('警告状态示例')
  const successStateValue = ref('成功状态示例')

  // 验证演示
  const emailValidateValue = ref('')
  const passwordValidateValue = ref('')
  const phoneValidateValue = ref('')

  // 方法测试
  const methodValue = ref('测试方法调用')

  // 插槽演示
  const prefixSlotValue = ref('')
  const suffixSlotValue = ref('')
  const appendTextValue = ref('')
  const appendMixedValue = ref('')
  const variantTextAppendValue = ref('')
  const prefixOuterSlotValue = ref('')
  const suffixOuterSlotValue = ref('')
  const fullSlotValue = ref('')

  // 短信验证码演示
  const smsCodeValue = ref('')
  const smsCodeVariantValue = ref('')
  const smsCodeStatusValue = ref('')
  const smsCodeCustomValue = ref('')

  // 搜索控件演示
  const basicSearchValue = ref('')
  const smartSearchValue = ref('')
  const advancedSearchValue = ref('')
  const searchStatusValue = ref('')
  const historySearchValue = ref('')
  const variantSearchValue = ref('')
  const mixedSearchValue = ref('')
  const manualLoadingValue = ref('')
  const manualLoading = ref(false)

  const searchResults = ref<string[]>([])
  const searchHistory = ref([
    'Vue.js',
    'React',
    'Angular',
    'TypeScript',
    'JavaScript',
  ])

  // 前缀功能演示
  const textPrefixValue = ref('')
  const currencyPrefixValue = ref('')
  const urlPrefixValue = ref('')
  const combinedPrefixValue = ref('')
  const statusPrefixValue = ref('')
  const customPrefixValue = ref('')

  // 后缀功能演示
  const textSuffixValue = ref('')
  const unitSuffixValue = ref('')
  const percentSuffixValue = ref('')
  const prefixSuffixComboValue2 = ref('')
  const variantSuffixValue2 = ref('')
  const statusSuffixValue2 = ref('')
  const customSuffixValue = ref('')
  const filledPrefixSuffixValue = ref('')

  // 电话号码输入演示
  const telValue = ref('')
  const telVariantValue = ref('')
  const telStatusValue = ref('')

  // 外部前缀图标演示
  const outerPrefixValue = ref('')
  const innerPrefixValue = ref('')
  const doublePrefixValue = ref('')
  const variantPrefixValue = ref('')
  const responsivePrefixValue = ref('')
  const compatibilityValue = ref('')

  // 外部后缀图标演示
  const outerSuffixValue = ref('')
  const innerSuffixValue = ref('')
  const doubleSuffixValue = ref('')
  const prefixSuffixComboValue = ref('')
  const variantSuffixValue = ref('')
  const responsiveSuffixValue = ref('')
  const compatibilitySuffixValue = ref('')
  const stateSuffixValue = ref('')

  // 组件引用
  const methodFieldRef = ref()

  // 验证规则
  const emailRules = 'required|email'
  const emailValidationRules = 'required|email'
  const passwordValidationRules = 'required|min:8'
  const phoneValidationRules = 'required|regex:^1[3-9]\\d{9}$'

  // 事件处理
  const handleChange = (value: string | number | undefined) => {
    console.log('InputField change:', value)
  }

  // 加载控制
  const toggleLoading = () => {
    isToggleLoading.value = !isToggleLoading.value
  }

  // 方法调用
  const focusField = () => {
    methodFieldRef.value?.focus()
  }

  const blurField = () => {
    methodFieldRef.value?.blur()
  }

  const selectField = () => {
    methodFieldRef.value?.select()
  }

  const clearField = () => {
    methodFieldRef.value?.clear()
  }

  const validateField = async () => {
    const isValid = await methodFieldRef.value?.validate()
    console.log('验证结果:', isValid)
  }

  // 主题切换方法
  const switchTheme = (themeName: string) => {
    currentTheme.value = themeName
    applyTheme(themeName)

    // 同步自定义颜色
    const theme = availableThemes.find(t => t.name === themeName)
    if (theme) {
      customThemeColor.value = theme.color
    }
  }

  // 应用自定义主题
  const applyCustomTheme = () => {
    currentTheme.value = 'custom'
    applyTheme(customThemeColor.value)
  }

  // 获取文本颜色（根据背景色自动判断）
  const getTextColor = (bgColor: string): string => {
    // 将颜色转换为RGB
    const hex = bgColor.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)

    // 计算亮度 (0-255)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000

    // 亮度大于128使用黑色文字，否则使用白色
    return brightness > 128 ? '#000000' : '#ffffff'
  }

  // 插槽演示方法
  const handleCustomAction = () => {
    console.log('自定义操作按钮点击')
  }

  const clearFullSlot = () => {
    fullSlotValue.value = ''
  }

  const handleFullSlotAction = () => {
    console.log('完整插槽操作按钮点击')
  }

  const handleMixedAppendClick = () => {
    console.log('混合后缀图标点击')
  }

  // 短信验证码相关方法
  const handleSendSmsCode = () => {
    console.log('发送短信验证码')
    // 这里可以调用实际的发送短信验证码 API
    // sendSmsCodeAPI(phoneNumber)
  }

  const handleSmsCountdownStart = (countdown: number) => {
    console.log('短信验证码倒计时开始:', countdown)
  }

  const handleSmsCountdownTick = (remaining: number) => {
    console.log('短信验证码倒计时:', remaining)
  }

  const handleSmsCountdownEnd = () => {
    console.log('短信验证码倒计时结束')
  }

  // 图标点击事件演示
  const clickEventValue1 = ref('')
  const clickEventValue2 = ref('')
  const clickEventValue3 = ref('')
  const clickEventValue4 = ref('')
  const clickEventValue5 = ref('')
  const searchValue = ref('')

  const prefixClickCount = ref(0)
  const prefixInnerClickCount = ref(0)
  const suffixClickCount = ref(0)
  const suffixInnerClickCount = ref(0)

  const handlePrefixClick = (event: MouseEvent) => {
    prefixClickCount.value++
    console.log('外部前缀图标点击', event)
  }

  const handlePrefixInnerClick = (event: MouseEvent) => {
    prefixInnerClickCount.value++
    console.log('内部前缀图标点击', event)
  }

  const handleSuffixClick = (event: MouseEvent) => {
    suffixClickCount.value++
    console.log('外部后缀图标点击', event)
  }

  const handleSuffixInnerClick = (event: MouseEvent) => {
    suffixInnerClickCount.value++
    console.log('内部后缀图标点击', event)
  }

  // 实际使用场景的方法
  const startSearch = () => {
    console.log('开始搜索:', searchValue.value)
    // 实际搜索逻辑
  }

  const toggleEmailFilter = () => {
    console.log('切换邮件过滤器')
    // 切换邮件过滤逻辑
  }

  const openSettings = () => {
    console.log('打开设置')
    // 打开设置面板
  }

  const clearSearch = () => {
    searchValue.value = ''
    console.log('清除搜索内容')
  }

  // 搜索控件相关方法
  const handleBasicSearch = () => {
    console.log('执行基础搜索:', basicSearchValue.value)
    if (basicSearchValue.value.trim()) {
      searchHistory.value.unshift(basicSearchValue.value)
      // 保持历史记录最多10条
      if (searchHistory.value.length > 10) {
        searchHistory.value = searchHistory.value.slice(0, 10)
      }
    }
  }

  const handleSmartSearch = () => {
    console.log('执行智能搜索:', smartSearchValue.value)
    updateSearchResults(smartSearchValue.value)
  }

  const clearSmartSearch = () => {
    smartSearchValue.value = ''
    searchResults.value = []
    console.log('清除智能搜索内容')
  }

  const handleSearchInput = (value: string | number | undefined) => {
    // 实时搜索，模拟搜索结果
    updateSearchResults(String(value || ''))
  }

  const updateSearchResults = (query: string) => {
    if (!query.trim()) {
      searchResults.value = []
      return
    }

    // 模拟搜索结果
    const mockResults = [
      'Vue.js 入门教程',
      'React 组件开发',
      'Angular 实战指南',
      'TypeScript 类型系统',
      'JavaScript 高级特性',
      'Node.js 后端开发',
      'CSS 动画效果',
      'HTML5 新特性',
      '前端性能优化',
      '组件库设计',
    ]

    searchResults.value = mockResults.filter(item =>
      item.toLowerCase().includes(query.toLowerCase())
    )
  }

  const handleAdvancedSearch = () => {
    console.log('执行高级搜索:', advancedSearchValue.value)
    if (advancedSearchValue.value.trim()) {
      searchHistory.value.unshift(advancedSearchValue.value)
      if (searchHistory.value.length > 10) {
        searchHistory.value = searchHistory.value.slice(0, 10)
      }
    }
  }

  const openSearchSettings = () => {
    console.log('打开搜索设置')
    // 这里可以打开搜索设置弹窗
  }

  const handleSearchWithLoading = () => {
    console.log('执行带加载状态的搜索:', searchStatusValue.value)
    // 搜索 loading 现在由组件自动管理
    // 只需要处理搜索逻辑即可
    if (searchStatusValue.value.trim()) {
      searchHistory.value.unshift(searchStatusValue.value)
      if (searchHistory.value.length > 10) {
        searchHistory.value = searchHistory.value.slice(0, 10)
      }
    }
  }

  const handleHistorySearch = () => {
    console.log('执行历史搜索:', historySearchValue.value)
    if (historySearchValue.value.trim()) {
      searchHistory.value.unshift(historySearchValue.value)
      if (searchHistory.value.length > 10) {
        searchHistory.value = searchHistory.value.slice(0, 10)
      }
    }
  }

  const showSearchHistory = () => {
    console.log('显示搜索历史:', searchHistory.value)
    // 这里可以显示搜索历史下拉列表
  }

  const showSearchSuggestions = () => {
    console.log('显示搜索建议')
    // 这里可以显示搜索建议列表
  }

  const handleMixedSearch = () => {
    console.log('执行混合功能搜索:', mixedSearchValue.value)
    if (mixedSearchValue.value.trim()) {
      searchHistory.value.unshift(mixedSearchValue.value)
      if (searchHistory.value.length > 10) {
        searchHistory.value = searchHistory.value.slice(0, 10)
      }
    }
  }

  const clearMixedSearch = () => {
    mixedSearchValue.value = ''
    console.log('清除混合搜索内容')
  }

  const handleVariantSearch = () => {
    console.log('执行变体样式搜索:', variantSearchValue.value)
    if (variantSearchValue.value.trim()) {
      searchHistory.value.unshift(variantSearchValue.value)
      if (searchHistory.value.length > 10) {
        searchHistory.value = searchHistory.value.slice(0, 10)
      }
    }
  }

  const handleManualLoadingSearch = () => {
    console.log('执行手动 loading 搜索:', manualLoadingValue.value)
    if (manualLoadingValue.value.trim()) {
      searchHistory.value.unshift(manualLoadingValue.value)
      if (searchHistory.value.length > 10) {
        searchHistory.value = searchHistory.value.slice(0, 10)
      }
    }
  }

  const toggleManualLoading = () => {
    manualLoading.value = !manualLoading.value
    console.log('手动 loading 状态:', manualLoading.value)
  }
</script>

<style scoped>
  .page-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .page-container h1 {
    color: #303133;
    font-size: 28px;
    margin-bottom: 10px;
  }

  .page-container > p {
    color: #606266;
    font-size: 14px;
    margin-bottom: 30px;
  }

  .demo-section {
    margin-bottom: 40px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    overflow: hidden;
  }

  .demo-section h2 {
    margin: 0;
    padding: 15px 20px;
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    color: #303133;
    font-size: 18px;
    font-weight: 600;
  }

  .demo-item {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: flex-start;
    gap: 15px;
  }

  .demo-item:last-child {
    border-bottom: none;
  }

  .demo-item label {
    width: 120px;
    flex-shrink: 0;
    font-weight: 500;
    color: #606266;
    font-size: 14px;
    line-height: 2.5;
  }

  .demo-item .sp-input-field {
    min-width: 280px;
  }

  .value-display {
    color: #909399;
    font-size: 12px;
    line-height: 2.5;
    margin-left: 10px;
  }

  .button-group {
    display: flex;
    gap: 8px;
    margin-left: 10px;
    align-items: center;
  }

  .button-group button {
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #fff;
    cursor: pointer;
    font-size: 12px;
    color: #606266;
    transition: all 0.2s;
  }

  .button-group button:hover {
    border-color: #409eff;
    color: #409eff;
  }

  .api-info {
    padding: 20px;
  }

  .api-info h3 {
    margin: 20px 0 10px 0;
    color: #303133;
    font-size: 16px;
  }

  .api-info h3:first-child {
    margin-top: 0;
  }

  .api-info code {
    background-color: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #e74c3c;
  }

  .api-info ul {
    margin: 0;
    padding-left: 20px;
  }

  .api-info li {
    margin: 5px 0;
    font-size: 14px;
    color: #606266;
  }

  /* 动态尺寸样式 */
  .size-slider {
    width: 200px;
    height: 6px;
    border-radius: 3px;
    background: #dcdfe6;
    outline: none;
    cursor: pointer;
    appearance: none;
  }

  .size-slider::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #409eff;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .size-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #409eff;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .size-comparison {
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 400px;
  }

  .size-comparison .sp-input-field {
    min-width: auto;
  }

  /* 主题切换器样式 */
  .theme-selector {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
  }

  .theme-btn {
    padding: 6px 12px;
    border: 2px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 60px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .theme-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .theme-btn.active {
    border-color: #ffffff;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3), 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }

  .color-picker {
    width: 60px;
    height: 30px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .color-picker:hover {
    border-color: #409eff;
  }

  .theme-showcase {
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 350px;
  }

  .theme-showcase .sp-input-field {
    min-width: auto;
  }

  /* 新增：外部前缀图标演示样式 */
  .variant-showcase {
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 400px;
  }

  .variant-showcase .sp-input-field {
    min-width: auto;
  }

  .responsive-showcase {
    display: flex;
    flex-direction: column;
    gap: 12px;
    min-width: 350px;
  }

  .responsive-showcase .sp-input-field {
    min-width: auto;
  }

  /* 代码示例样式 */
  .code-example {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin: 12px 0;
    overflow-x: auto;
  }

  .code-example pre {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    color: #24292e;
  }

  .code-example code {
    background: none;
    color: inherit;
    padding: 0;
    font-size: inherit;
  }

  /* 自定义插槽样式 */
  .custom-prepend-outer,
  .custom-append-outer {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .custom-text {
    font-size: 12px;
    color: #666;
    font-weight: 500;
  }

  .custom-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: #f9fafb;
    color: #374151;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .custom-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
  }

  .custom-btn:active {
    background: #e5e7eb;
    transform: translateY(1px);
  }

  /* 前缀功能演示样式 */
  .custom-prefix {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .prefix-text {
    font-size: 12px;
    color: #666;
    font-weight: 500;
  }

  /* 后缀文字样式 */
  .append-text {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
    white-space: nowrap;
    user-select: none;
    line-height: 1;
  }
</style>
