@use './common/var.scss' as *;

// ===== FieldContainer 基础变量 =====
:root {
  --sp-color-white: #ffffff;
}

// ===== FieldContainer 主样式 =====
.sp-field-container {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;

  // ===== 整体布局容器 =====
  &__layout {
    display: flex;
    align-items: stretch;
    gap: 12px; // 外部图标与输入框的间距
    width: 100%;
  }

  // ===== 外部前置图标 =====
  &__prepend-icon-outer {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0; // 不压缩图标区域
    pointer-events: auto; // 允许点击插槽内容
    transition: all 0.3s ease;
    min-height: var(--sp-field-size, 48px); // 与输入框高度一致
    color: $color-text-secondary; // 直接设置灰色
  }

  // ===== 外部后置图标 =====
  &__append-icon-outer {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0; // 不压缩图标区域
    pointer-events: auto; // 允许点击插槽内容
    transition: all 0.3s ease;
    min-height: var(--sp-field-size, 48px); // 与输入框高度一致
    color: $color-text-secondary; // 直接设置灰色
  }

  // ===== 外部图标状态样式 =====
  // 聚焦状态
  &--focused {
    .sp-field-container__prepend-icon-outer,
    .sp-field-container__append-icon-outer {
      :deep(svg),
      :deep(.sp-icon),
      :deep(i) {
        color: $color-primary;
      }
    }
  }

  // 禁用状态
  &--disabled {
    .sp-field-container__prepend-icon-outer,
    .sp-field-container__append-icon-outer {
      :deep(svg),
      :deep(.sp-icon),
      :deep(i) {
        color: $color-text-disabled;
        cursor: not-allowed;
      }
    }
  }

  // 错误状态
  &--error {
    .sp-field-container__prepend-icon-outer,
    .sp-field-container__append-icon-outer {
      :deep(svg),
      :deep(.sp-icon),
      :deep(i) {
        color: $color-danger;
      }
    }
  }

  // 警告状态
  &--warning {
    .sp-field-container__prepend-icon-outer,
    .sp-field-container__append-icon-outer {
      :deep(svg),
      :deep(.sp-icon),
      :deep(i) {
        color: $color-warning;
      }
    }
  }

  // 成功状态
  &--success {
    .sp-field-container__prepend-icon-outer,
    .sp-field-container__append-icon-outer {
      :deep(svg),
      :deep(.sp-icon),
      :deep(i) {
        color: $color-success;
      }
    }
  }

  // ===== 字段容器（用于包含输入框和相关元素） =====
  &__field-wrapper {
    position: relative;
    flex: 1; // 占据剩余空间
    min-width: 0; // 允许收缩
    display: flex;
    align-items: center;
    width: 100%;
    transition: border-color 0.3s ease, box-shadow 0.3s ease,
      background-color 0.3s ease;
    cursor: text;
    // 使用固定高度而不是最小高度，确保尺寸一致性
    height: var(--sp-field-size, 40px); // 支持动态尺寸
    overflow: visible;
    box-sizing: border-box;
  }

  // ===== 通用滚动条美化 =====
  // 为所有可滚动元素提供统一的滚动条样式
  * {
    scrollbar-width: thin; // Firefox
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent;

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid transparent;
      background-clip: padding-box;
      transition: background-color 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.35);
      }

      &:active {
        background: rgba(0, 0, 0, 0.5);
      }
    }

    &::-webkit-scrollbar-corner {
      background: transparent;
    }
  }

  // ===== 输入容器 =====
  &__input-container {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
  }

  // ===== 浮动标签 =====
  &__label {
    position: absolute;
    left: 14px; // 相对于输入容器定位
    top: 50%; // 非浮动状态下居中显示
    color: $color-text-secondary;
    font-size: var(--sp-field-label-font-size, 16px);
    transition: top 0.25s cubic-bezier(0.4, 0, 0.2, 1),
      transform 0.25s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.25s cubic-bezier(0.4, 0, 0.2, 1),
      font-weight 0.25s cubic-bezier(0.4, 0, 0.2, 1),
      left 0.25s cubic-bezier(0.4, 0, 0.2, 1),
      opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1); // 添加透明度过渡
    pointer-events: none;
    transform-origin: left top;
    transform: translateY(-50%); // 初始状态垂直居中

    z-index: 3;
    user-select: none;
    cursor: text;
    // background: #000;
    padding: 0 4px;
    border-radius: 2px;
    white-space: nowrap;

    // 必填星号
    &-asterisk {
      color: $color-danger;
      margin-right: 2px;
    }

    // 浮动状态
    &--floating {
      top: 0;
      transform: translateY(-50%);
      transform-origin: center;
      font-size: 12px;
      color: $color-text-secondary;
      font-weight: 500;
    }
  }

  // ===== 没有前置图标时的默认变体 label 优化 =====
  // &--default:not(.sp-field-container--has-prepend) {
  //   .sp-field-container__label--floating {
  //     // 没有前置图标时，让 label 移动到 outline 位置然后隐藏
  //     // 这样与 outline placeholder 的出现更加衔接
  //     transform: translateY(-50%) scale(1);
  //     opacity: 0;
  //     transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  //   }
  // }

  // ===== 聚焦状态下的标签颜色 =====
  &--focused {
    .sp-field-container__label {
      color: $color-primary;
    }

    .sp-field-container__prefix {
      color: $color-primary;
      opacity: 1; // 聚焦时显示前缀
    }

    .sp-field-container__suffix {
      color: $color-primary;
      opacity: 1; // 聚焦时显示后缀
    }

    // 聚焦时显示输入框
    .sp-field-container__input {
      opacity: 1;
    }
  }

  // ===== 有值时显示输入框和前缀后缀 =====
  &--has-value {
    .sp-field-container__input {
      opacity: 1;
    }

    .sp-field-container__prefix {
      opacity: 1; // 有值时显示前缀
    }

    .sp-field-container__suffix {
      opacity: 1; // 有值时显示后缀
    }
  }

  // ===== 有后缀且标签浮动时的样式 =====
  &--suffix-floating {
    .sp-field-container__label {
      &--floating {
        // transform: translateY(-50%) translateX(-4px) scale(0.85);
      }
    }
  }

  // ===== 默认变体有前缀时的输入框样式 =====
  &--default.sp-field-container--prefix-floating {
    .sp-field-container__input {
      padding-left: 5px; // 有前缀时减少左边距
    }
  }

  // ===== 默认变体有后缀时的输入框样式 =====
  &--default.sp-field-container--suffix-floating {
    .sp-field-container__input {
      padding-right: 5px; // 有后缀时减少右边距
    }
  }

  // ===== 默认变体有前缀+后缀时的输入框样式 =====
  &--default.sp-field-container--prefix-floating.sp-field-container--suffix-floating {
    .sp-field-container__input {
      padding-left: 5px;
      padding-right: 5px;
    }
  }

  // ===== 输入元素基础样式 =====
  &__input {
    flex: 1;
    width: 100%;
    // padding: 15px;
    padding: var(--sp-field-padding-top, 24px) 20px
      var(--sp-field-padding-bottom, 4px) 20px;
    border: none;
    outline: none;
    background: transparent;
    color: $color-text-primary;
    // font-size: var(--sp-field-font-size, 20px); // 支持动态字体大小
    font-family: inherit;
    line-height: 1.3;
    transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    transform: none;
    will-change: auto;
    position: relative;
    z-index: 1; // 基础层级，确保在标签和功能区域之下
    box-sizing: border-box;
    border-radius: inherit; // 继承父容器的圆角，解决自动填充覆盖圆角的问题
    opacity: 0; // 默认透明，只有激活时才显示

    &::placeholder {
      color: $color-text-secondary;
      opacity: 0.8;
    }

    // 隐藏浏览器默认的密码显示/隐藏图标
    &::-webkit-textfield-decoration-container {
      display: none !important;
    }

    &::-webkit-credentials-auto-fill-button {
      display: none !important;
    }

    &::-ms-reveal {
      display: none !important;
    }

    // 隐藏 number input 的原生箭头
    &[type='number'] {
      -moz-appearance: textfield; // Firefox

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none; // Chrome, Safari
        margin: 0;
      }
    }
    // 隐藏浏览器原生搜索清除按钮
    &[type='search']::-webkit-search-cancel-button {
      -webkit-appearance: none;
      appearance: none;
      display: none;
    }

    // 隐藏 Edge 浏览器的清除按钮
    &[type='search']::-ms-clear {
      display: none;
    }
    &:disabled {
      color: $color-text-disabled;
      cursor: not-allowed;

      &::placeholder {
        color: $color-text-disabled;
      }
    }

    &:read-only {
      cursor: default;
    }
  }

  // ===== 前置区域 =====
  &__prepend {
    display: flex;
    align-items: center;
    gap: 8px;
    color: $color-text-secondary;
    pointer-events: none;
    flex-shrink: 0; // 不压缩前置区域
    // padding: 0 0 0 18px; // 简洁的 padding
    opacity: 1; // 始终显示前置图标，与后置区域保持一致
    transition: opacity 0.2s ease;

    // 前置元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 前缀区域 =====
  &__prefix {
    display: flex;
    align-items: center;
    gap: 4px;
    color: $color-text-secondary;
    pointer-events: none;
    font-size: var(--sp-field-font-size, 16px);
    font-weight: 500;
    line-height: 1;
    white-space: nowrap;
    transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s ease;
    // padding-left: 18px; // 与输入框左边距对齐
    flex-shrink: 0; // 不压缩前缀
    opacity: 0; // 默认透明，只有激活时才显示

    // 前缀元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 后缀区域 =====
  &__suffix {
    display: flex;
    align-items: center;
    gap: 4px;
    color: $color-text-secondary;
    pointer-events: none;
    font-size: var(--sp-field-font-size, 16px);
    font-weight: 500;
    line-height: 1;
    white-space: nowrap;
    transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s ease;
    padding-right: 24px; // 与输入框右边距对齐
    flex-shrink: 0; // 不压缩后缀
    opacity: 0; // 默认透明，只有激活时才显示

    // 后缀元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 后置区域 =====
  &__append {
    display: flex;
    align-items: center;
    gap: 8px;
    color: $color-text-secondary;
    pointer-events: none;
    flex-shrink: 0; // 不压缩后置区域
    padding: 0 20px 0 0; // 适当的内边距

    // 后置元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 功能区域（向后兼容） =====
  &__functions {
    position: absolute;
    top: 16px;
    right: 16px;
    bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-start;
    gap: 8px;
    color: $color-text-secondary;
    pointer-events: none;
    z-index: 2; // 保持在输入内容之上，但在标签之下

    // 功能元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 加载动画 =====
  &__loading-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    overflow: hidden;
    background: transparent;
  }

  &__loading-progress {
    height: 100%;
    background: $color-primary;
    width: 30%;
    animation: sp-field-container-loading 1.5s infinite ease-in-out;
  }

  // ===== 消息区域 =====
  &__message,
  &__helper {
    margin-top: 6px;
    font-size: 12px;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  &__message {
    color: $color-danger;

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  &__helper {
    color: $color-text-secondary;

    &--error {
      color: $color-danger;
    }

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  // ===== 变体样式 =====

  // 默认变体
  &--default {
    .sp-field-container__field-wrapper {
      border: 1px solid $border-color-base;
      border-radius: 6px;
      background: var(--sp-color-white);
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .sp-field-container__input {
      flex: 1; // 占据剩余空间
      // padding: calc(
      //     (
      //         var(--sp-field-padding-top, 24px) +
      //           var(--sp-field-padding-bottom, 4px)
      //       ) / 2
      //   )
      //   20px;
      // 0;
      padding: 15px 18px;
      border-radius: 6px;
    }

    // default 变体下的标签透明度效果
    .sp-field-container__label {
      &--floating {
        opacity: 0; // 浮动时完全透明
        // 向上移动并消失（调整为更自然的距离）
        // transform: translateY(-10px);
        transform-origin: center center;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
    }

    &:hover:not(.sp-field-container--focused):not(
        .sp-field-container--disabled
      ) {
      .sp-field-container__field-wrapper {
        border-color: #999999;
        box-shadow: 0 0 0 1px rgba(153, 153, 153, 0.2);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__field-wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }

    // .sp-field-container__label {
    //   left: -20px; // 激活时标签向左偏移 20px (16px - 20px = -4px)
    // }

    // .sp-field-container__label {
    //   &--floating {
    //     left: -20px;
    //     transform: translateY() scale(0.85);
    //   }
    // }
  }

  // 下划线变体
  &--underlined {
    .sp-field-container__field-wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 0;
      background: transparent;
      position: relative;
      transition: border-bottom-color 0.3s ease;
    }

    .sp-field-container__input {
      border-radius: 0;
    }

    .sp-field-container__label {
      background: transparent;
      padding: 0;
      top: 50%; // 与默认变体保持一致的居中位置
      transform: translateY(-50%); // 确保垂直居中

      &--floating {
        top: 4px;
        transform: translateY(0) scale(0.85);
      }
    }

    // 下划线变体的前缀浮动样式
    &.sp-field-container--prefix-floating {
      .sp-field-container__input {
        padding-left: 60px;
      }

      .sp-field-container__label {
        &--floating {
          transform: translateY(0) translateX(4px) scale(0.85);
        }
      }
    }

    // 下划线变体的后缀浮动样式
    &.sp-field-container--suffix-floating {
      .sp-field-container__input {
        padding-right: 60px;
      }

      .sp-field-container__label {
        &--floating {
          // transform: translateY(0) translateX(-4px) scale(0.85);
        }
      }
    }

    // 下划线变体的前缀+后缀浮动样式
    &.sp-field-container--prefix-floating.sp-field-container--suffix-floating {
      .sp-field-container__input {
        padding-left: 60px;
        padding-right: 60px;
      }
    }

    .sp-field-container__field-wrapper::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: $color-primary;
      transform: scaleX(0);
      transform-origin: center;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:hover:not(.sp-field-container--focused):not(
        .sp-field-container--disabled
      ) {
      .sp-field-container__field-wrapper {
        border-bottom-color: #999999;
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__field-wrapper {
        border-bottom-color: $color-primary;
      }

      .sp-field-container__field-wrapper::after {
        transform: scaleX(1);
      }
    }
  }

  // 填充变体
  &--filled {
    .sp-field-container__field-wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 6px 6px 0 0;
      background: $background-color-hover;
      position: relative;
      transition: background-color 0.3s ease, border-bottom-color 0.3s ease;
    }

    .sp-field-container__input {
      border-radius: 6px 6px 0 0;
    }

    .sp-field-container__label {
      background: transparent;
      padding: 0;
      top: 50%; // 与默认变体保持一致的居中位置
      transform: translateY(-50%); // 确保垂直居中

      &--floating {
        top: 4px;
        transform: translateY(0) scale(0.85);
      }
    }

    // 填充变体的前缀浮动样式
    &.sp-field-container--prefix-floating {
      .sp-field-container__input {
        padding-left: 5px;
      }

      .sp-field-container__label {
        &--floating {
          transform: translateY(0) translateX(4px) scale(0.85);
        }
      }

      .sp-field-container__prefix {
        padding-top: 20px;
      }
    }

    // 填充变体的后缀浮动样式
    &.sp-field-container--suffix-floating {
      .sp-field-container__input {
        padding-right: 5px;
      }

      .sp-field-container__label {
        &--floating {
          // transform: translateY(0) translateX(-4px) scale(0.85);
        }
      }

      .sp-field-container__suffix {
        padding-top: 24px;
      }
    }

    // 填充变体的前缀+后缀浮动样式
    &.sp-field-container--prefix-floating.sp-field-container--suffix-floating {
      .sp-field-container__input {
        padding-left: 5px;
        padding-right: 5px;
      }
    }

    .sp-field-container__field-wrapper::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 1px;
      background-color: $color-primary;
      transform: scaleX(0);
      transform-origin: center;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:hover:not(.sp-field-container--focused):not(
        .sp-field-container--disabled
      ) {
      .sp-field-container__field-wrapper {
        background: #e8e8e8;
        border-bottom-color: #999999;
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__field-wrapper {
        border-bottom-color: $color-primary;
        background: #e4e7ed;
      }

      .sp-field-container__field-wrapper::after {
        transform: scaleX(1);
      }
    }
  }

  // 无边框变体
  &--unborder {
    .sp-field-container__field-wrapper {
      border: none;
      border-radius: 6px;
      background: transparent;
      transition: background-color 0.3s ease;
    }

    .sp-field-container__input {
      border-radius: 6px;
    }

    .sp-field-container__label {
      background: transparent;
      padding: 0;
      top: 50%; // 与默认变体保持一致的居中位置
      transform: translateY(-50%); // 确保垂直居中

      &--floating {
        top: 4px;
        transform: translateY(0) scale(0.85);
      }
    }

    // 无边框变体的前缀浮动样式
    &.sp-field-container--prefix-floating {
      .sp-field-container__input {
        padding-left: 60px;
      }

      .sp-field-container__label {
        &--floating {
          transform: translateY(0) translateX(4px) scale(0.85);
        }
      }
    }

    // 无边框变体的后缀浮动样式
    &.sp-field-container--suffix-floating {
      .sp-field-container__input {
        padding-right: 60px;
      }

      .sp-field-container__label {
        &--floating {
          // transform: translateY(0) translateX(-4px) scale(0.85);
        }
      }
    }

    // 无边框变体的前缀+后缀浮动样式
    &.sp-field-container--prefix-floating.sp-field-container--suffix-floating {
      .sp-field-container__input {
        padding-left: 60px;
        padding-right: 60px;
      }
    }

    &:hover:not(.sp-field-container--focused):not(
        .sp-field-container--disabled
      ) {
      .sp-field-container__field-wrapper {
        background: rgba(245, 245, 245, 0.6);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__field-wrapper {
        background: transparent;
      }
    }
  }

  // ===== 状态样式 =====
  &--error {
    .sp-field-container__field-wrapper {
      border-color: $color-danger !important;
    }

    .sp-field-container__label {
      color: $color-danger !important;
    }

    .sp-field-container__prefix {
      color: $color-danger !important;
    }

    .sp-field-container__suffix {
      color: $color-danger !important;
    }
  }

  &--warning {
    .sp-field-container__field-wrapper {
      border-color: $color-warning !important;
    }

    .sp-field-container__label {
      color: $color-warning !important;
    }

    .sp-field-container__prefix {
      color: $color-warning !important;
    }

    .sp-field-container__suffix {
      color: $color-warning !important;
    }
  }

  &--success {
    .sp-field-container__field-wrapper {
      border-color: $color-success !important;
    }

    .sp-field-container__label {
      color: $color-success !important;
    }

    .sp-field-container__prefix {
      color: $color-success !important;
    }

    .sp-field-container__suffix {
      color: $color-success !important;
    }
  }

  // ===== 禁用状态 =====
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;

    .sp-field-container__field-wrapper {
      background: $background-color-disabled;
      border-color: $border-color-disabled;
    }

    .sp-field-container__input {
      cursor: not-allowed;
    }

    .sp-field-container__label {
      color: $color-text-disabled;
    }

    .sp-field-container__prefix {
      color: $color-text-disabled;
    }

    .sp-field-container__suffix {
      color: $color-text-disabled;
    }
  }

  // ===== Outline 轮廓线系统（只是边框和缺口） =====
  &__outline {
    position: absolute;
    top: 0;
    // 动态计算左边距，跳过外部前置图标
    left: var(--sp-field-prepend-outer-width, 0px);
    // 动态计算右边距，跳过外部后置图标
    right: var(--sp-field-append-outer-width, 0px);
    bottom: 0;
    pointer-events: none;
    display: flex;
    align-items: stretch;
    border-radius: 6px;
    z-index: 2; // 提高层级，确保在wrapper之上

    &__start {
      flex: 0 0 15px;
      // 默认状态显示正常边框，激活时只改变颜色
      border-left: 1px solid $border-color-base;
      border-bottom: 1px solid $border-color-base;
      border-top: 1px solid $border-color-base;
      // 右边无边框，连接 notch
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      transition: border-color 0.3s ease;
    }

    &__end {
      flex: 1;
      // 默认状态显示正常边框，激活时只改变颜色
      border-right: 1px solid $border-color-base;
      border-bottom: 1px solid $border-color-base;
      border-top: 1px solid $border-color-base;
      // 左边无边框，连接 notch
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
      transition: border-color 0.3s ease;
    }

    &__notch {
      flex: 0 0 auto;
      // 宽度由占位元素自动撑开
      // 默认状态显示正常边框，激活时只改变颜色
      border-bottom: 1px solid $border-color-base;
      // 上边、左边、右边都无边框，只有下边有边框
      position: relative;
      transition: border-color 0.3s ease;

      // 占位元素样式
      &-placeholder {
        display: block; // 始终显示，占据空间
        font-size: 12px;
        color: transparent; // 默认透明

        // 默认位置：没有前置图标时从下方出现，有前置图标时从右下角出现
        transform: translateY(-20%) translateX(0) scale(1); // 默认从下方向上移入
        margin: 0 4px; // 与标签相同的 padding
        white-space: nowrap;
        user-select: none;
        pointer-events: none;
        opacity: 1; // 默认完全透明
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); // 不需要CSS延迟，由JS控制

        // 激活状态：移动到中心并显示
        &--visible {
          color: $color-primary; // 显示颜色
          opacity: 1; // 显示
          transform: translateY(-50%) translateX(0) scale(1); // 移动到中心位置并恢复大小
        }

        // 必填星号样式
        .sp-field-container__label-asterisk {
          color: transparent; // 默认透明
        }

        // 激活时星号显示
        &--visible .sp-field-container__label-asterisk {
          color: $color-danger;
        }
      }
    }
  }

  // ===== 有前置图标时的通用样式 =====
  &--has-prepend {
    // 有前置内部图标时，始终添加左内边距
    .sp-field-container__field-wrapper {
      padding-left: 18px;
    }
  }

  // ===== 只有前缀且激活时的样式 =====
  &--prefix-floating:not(.sp-field-container--has-prepend) {
    // 只有前缀文本且激活时，给前缀本身添加左内边距
    .sp-field-container__prefix {
      padding-left: 18px;
    }
  }

  // ===== 只有前缀时的简单处理 =====
  &--prefix-floating.sp-field-container--default:not(
      .sp-field-container--has-prepend
    ) {
    .sp-field-container__label--floating {
      // 只有前缀时，label 瞬间隐身，不做复杂动画
      opacity: 0;
      // transition: opacity 0.1s ease;
    }
  }

  // ===== 有前置图标时的 label 偏移（仅默认变体，排除只有前缀的情况） =====
  &--has-prepend.sp-field-container--default:not(
      .sp-field-container--prefix-floating
    ) {
    .sp-field-container__label--floating {
      // 当有前置图标时，label向左上方移动并消失
      // 使用CSS calc()动态计算，基于前置区域的实际宽度
      transform: translate(calc(-1 * var(--prepend-width, 50px) - 1px), -10px);
      transform-origin: center center;
      transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      // 确保移动后隐藏，避免与 outline placeholder 重复
      opacity: 0;
    }

    // 有前置图标时，placeholder从右下角出现
    .sp-field-container__outline__notch-placeholder {
      // 默认位置：右下角更下方
      transform: translateY(-20%) translateX(15px) scale(1);

      // 激活状态：从右下角移动到中心
      &--visible {
        transform: translateY(-50%) translateX(0) scale(1);
      }
    }
  }

  // ===== 有前置图标且有前缀时的 label 偏移（仅默认变体） =====
  &--has-prepend.sp-field-container--prefix-floating.sp-field-container--default {
    .sp-field-container__label--floating {
      // 当同时有前置图标和前缀时，label向左上方移动并消失
      // 使用CSS calc()动态计算，基于前置区域的实际宽度
      transform: translate(calc(-1 * var(--prepend-width, 50px) - 1px), -10px);
      transform-origin: center center;
      transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      // 确保移动后隐藏，避免与 outline placeholder 重复
      opacity: 0;
    }

    // 有前置图标时，placeholder从右下角出现
    .sp-field-container__outline__notch-placeholder {
      // 默认位置：右下角更下方
      transform: translateY(-20%) translateX(15px) scale(1);

      // 激活状态：从右下角移动到中心
      &--visible {
        transform: translateY(-50%) translateX(0) scale(1);
      }
    }
  }

  // ===== 当有 outline 时的边框处理 =====
  &--has-outline {
    .sp-field-container__field-wrapper {
      // 完全隐藏wrapper的边框，让outline接管
      border: 1px solid transparent !important;
      box-shadow: none !important;
      // 保持过渡动画，让切换更平滑
      transition: border-color 0.3s ease, box-shadow 0.3s ease,
        background-color 0.3s ease;
      // 确保高度计算一致
      box-sizing: border-box;
    }
  }

  // ===== 聚焦状态的 outline 样式 =====
  &--focused {
    .sp-field-container__outline {
      &__start,
      &__end,
      &__notch {
        border-color: $color-primary;
      }
    }
  }

  // ===== 悬停状态的 outline 样式 =====
  &:hover:not(.sp-field-container--focused):not(.sp-field-container--disabled) {
    .sp-field-container__outline {
      &__start,
      &__end,
      &__notch {
        border-color: #999999;
      }
    }
  }

  // ===== 有前置图标时的 outline placeholder 偏移（放在最后确保优先级） =====
  // &--has-prepend.sp-field-container--default {
  //   .sp-field-container__outline {
  //     .sp-field-container__outline__notch {
  //       .sp-field-container__outline__notch-placeholder {
  //         // 当有前置图标时，placeholder需要向右偏移避免与图标重叠
  //         transform: translateY(-50%) translateX(30px) !important;
  //       }
  //     }
  //   }
  // }

  // ===== 动画 =====
  @keyframes sp-field-container-loading {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(300%);
    }
  }

  // ===== 消息动画 =====
  .sp-field-container-message-enter-active,
  .sp-field-container-message-leave-active {
    transition: all 0.3s ease;
  }

  .sp-field-container-message-enter-from {
    opacity: 0;
    transform: translateY(-10px);
  }

  .sp-field-container-message-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

// ===== Textarea 特有样式 =====
.sp-textarea-field {
  &__textarea {
    // 继承 field-container 的输入样式，添加 textarea 特有属性
    min-height: inherit;
    resize: vertical; // 默认垂直调整大小
    font-family: inherit;
    line-height: 1.5;

    // ===== 美化滚动条样式 =====
    scrollbar-width: thin; // Firefox 滚动条宽度
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent; // Firefox 滚动条颜色

    // WebKit 滚动条样式 (Chrome, Safari, Edge)
    &::-webkit-scrollbar {
      width: 8px; // 垂直滚动条宽度
    }

    &::-webkit-scrollbar-track {
      background: transparent; // 滚动条轨道背景
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2); // 滚动条拇指颜色
      border-radius: 4px;
      border: 1px solid transparent;
      background-clip: padding-box;
      transition: background-color 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.35); // 悬停时的颜色
      }

      &:active {
        background: rgba(0, 0, 0, 0.5); // 按下时的颜色
      }
    }

    // 滚动条角落
    &::-webkit-scrollbar-corner {
      background: transparent;
    }

    // 在有浮动标签的变体中，限制内容显示区域
    .sp-field-container--filled &,
    .sp-field-container--underlined &,
    .sp-field-container--unborder & {
      // 使用 mask 来限制内容显示区域，但不影响滚动条
      mask: linear-gradient(
        to bottom,
        transparent 0px,
        transparent 28px,
        black 28px,
        black 100%
      );
      -webkit-mask: linear-gradient(
        to bottom,
        transparent 0px,
        transparent 28px,
        black 28px,
        black 100%
      );

      // 或者使用 padding + negative margin 的方法
      // padding-top: 28px;
      // margin-top: -28px;
      // box-sizing: border-box;
    }

    // 聚焦状态下的滚动条增强效果
    &:focus {
      &::-webkit-scrollbar-thumb {
        background: rgba($color-primary, 0.4);

        &:hover {
          background: rgba($color-primary, 0.6);
        }
      }
    }

    // 错误状态下的滚动条
    .sp-field-container--error & {
      &::-webkit-scrollbar-thumb {
        background: rgba($color-danger, 0.3);

        &:hover {
          background: rgba($color-danger, 0.5);
        }
      }
    }

    // 滚动条尺寸现在通过配置系统控制
  }

  &__clear {
    color: $color-text-secondary;
    cursor: pointer;
    transition: color 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    padding: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      color: $color-primary;
      background: rgba(255, 255, 255, 1);
      border-color: $color-primary;
    }
  }

  &__count {
    font-size: 11px;
    color: $color-text-secondary;
    white-space: nowrap;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 2px 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 4px;
  }
}

// ===== InputField 特有样式 =====
.sp-input-field {
  &__input {
    // 继承 field-container 的输入样式
  }

  &__prepend-icon,
  &__append-icon {
    color: $color-text-secondary;
    transition: color 0.3s ease;

    &:hover {
      color: $color-primary;
    }
  }

  &__clear,
  &__password {
    color: $color-text-secondary;
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: $color-primary;
    }
  }

  &__count {
    font-size: 11px;
    color: $color-text-secondary;
    white-space: nowrap;
  }
}

// ===== 主题适配滚动条样式 =====
// 深色主题滚动条
[data-theme='dark'] .sp-field-container,
.sp-field-container--dark-theme {
  * {
    scrollbar-color: rgba(255, 255, 255, 0.4) transparent;

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }

      &:active {
        background: rgba(255, 255, 255, 0.7);
      }
    }
  }

  .sp-textarea-field__textarea {
    scrollbar-color: rgba(255, 255, 255, 0.4) transparent;

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }

    &:focus {
      &::-webkit-scrollbar-thumb {
        background: rgba($color-primary, 0.6);

        &:hover {
          background: rgba($color-primary, 0.8);
        }
      }
    }
  }
}

// 高对比度主题滚动条
[data-theme='high-contrast'] .sp-field-container,
.sp-field-container--high-contrast {
  * {
    scrollbar-color: #000000 #ffffff;

    &::-webkit-scrollbar-thumb {
      background: #000000;
      border: 1px solid #ffffff;

      &:hover {
        background: #333333;
      }
    }

    &::-webkit-scrollbar-track {
      background: #ffffff;
      border: 1px solid #000000;
    }
  }
}

// 禁用状态滚动条样式
.sp-field-container--disabled {
  * {
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      cursor: not-allowed;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .sp-textarea-field__textarea {
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      cursor: not-allowed;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}
